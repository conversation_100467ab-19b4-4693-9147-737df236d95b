APP_DEBUG = true

ALIURL = https://images.wineyun.com
[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[ITEM]
#产品模块
COMMODITIES_URL = http://test-wine.wineyun.com/commodities
LIVE_URL = https://test-wine.wineyun.com/live
USER_URL = "http://test-wine.wineyun.com/user"

[DATABASE_MARKETING]

#营销
TYPE = mysql
HOSTNAME = *************
DATABASE = vh_marketing
USERNAME =  vinehoodev
PASSWORD = vinehoo123
HOSTPORT = 3306
CHARSET = utf8mb4
PREFIX = vh_
DEBUG = false

[CACHE]
DRIVER = redis
HOST = *************
PASSWORD = vh@123
PORT = 6379
prefix = "vinehoo."
db = 1

[CACHE]
DRIVER = redis
HOST = 127.0.0.1
PORT = 6379
prefix = "vinehoo."
db = 1

[NEO4J]
HOST = *************
PORT = 7687
USER = neo4j
PASS = vinehoo666


[RABBIMQ]
IP = *************
PORT = 5672
NAME = admin
PSWORD = vinehoo666
VHOST = /

[OSS]
ACCESSKEYID = LTAI5t88V4fhDjxmuuvmmV5r
ACCESSKEYSECRET = ******************************
ENDPOINT = https://images.wineyun.com
BUCKET = vinehoo-test
ALIURL = https://images.wineyun.com
ROLEARN = acs:ram::1083400064674077:role/oss-sts
ROLESESSIONNAME = oss-sts
REGIONID = cn-zhangjiakou
CALLBACK_URL="https://test-wine.wineyun.com/oss/oss/v3/callback"


[ES]
HOST = "*************"
PORT = 9200
USER = elastic
PASS = "vinehoo666"
PREFIX = "vinehoo."

[LANG]
default_lang = zh-cn