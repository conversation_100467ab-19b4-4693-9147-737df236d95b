<?php

namespace app;

use Exception;

class CustomException extends Exception
{
    protected $message;
    protected $level;
    protected $code;

    public function __construct($message, $level, $code) {
    	$this->message = $message;
        $this->level = $level;
    	$this->code = $code;
    }

    public function getErrCode() {
        return $this->code;
    }

    public function getError() {
        return $this->message;
    }

    public function getLevel() {
        return $this->level;
    }
}