<?php
declare (strict_types = 1);

namespace app\command;

use app\controller\v3\Ad;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;

class AdCache extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('adcache')
            ->setDescription('设置广告缓存');
    }

    protected function execute(Input $input, Output $output)
    {
        $ad = new \app\service\v3\Ad();
        $ad->cacheCreate();
        //修改广告状态 下架时间已到。并且是启用的广告
        \app\service\v3\Ad::taskstatusSave();
        // 指令输出
        $output->writeln('adcache');
    }
}
