<?php



declare(strict_types=1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\controller\v3\ElasticSearch;
use app\service\v3\Game;
use think\trace\TraceDebug;

class AwardCouponExpireMonitor extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('AwardCouponExpireMonitor')
            ->setDescription('奖项优惠券到期监控');
    }

    protected function execute(Input $input, Output $output)
    {
        $test_access_token = 'a5df4c1d-0f20-4f0c-8410-c91dca6d7695';
        $access_token = env('MARKETING.COUPON_MONITOR_TOKEN') ?? $test_access_token;

        $time = time();
        $table = [
            'rotary_draw',
            'rabbit_rotary_draw',
            'rabbit_rotary_draw_newcomer'
        ];

        $table_desc = [
            '支付后抽奖配置',
            '兔头大转盘配置',
            '新用户抽奖配置'
        ];
        foreach ($table as $k => $v) {
            $couponid_all = [];
            $couponid = Db::name($v)->where('type', 2)->column('number');
            foreach ($couponid as $vv) {
                $cid = explode(',', $vv);
                foreach ($cid as $vvv) {
                    if (!empty($vvv) && is_numeric($vvv)) {
                        $couponid_all[] = $vvv;
                    }
                }
            }
            if (!empty($couponid_all)) {
                $coupon_id = Db::name('coupon')
                    ->where([
                        ['id', 'in', $couponid_all],
                        ['invalidate_time', '>', $time],
                        ['invalidate_time', '<', intval($time + 86400)],
                    ])
                    ->column('id');
                if (!empty($coupon_id)) {
                    $coupon_id_str = implode(',', $coupon_id);
                    $content = "“{$table_desc[$k]}”中的 {$coupon_id_str} 优惠券 1 天后到期，为确保正常发放，请运营及时处理。";
                    SendWeChatRobot($content, $access_token);
                }
            }
        }

        echo 'OK';
    }
}
