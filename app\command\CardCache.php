<?php
declare (strict_types = 1);

namespace app\command;

use app\service\v3\AdExternal;
use app\service\v3\Card;
use app\service\v3\CardGoodsLive;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\facade\Cache;

class CardCache extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('cardCache')
            ->addOption('card',null,Argument::OPTIONAL,'default')
            ->setDescription('设置卡片缓存');
    }

    protected function execute(Input $input, Output $output)
    {

        $option = $input->getOption('card');


        switch ($option){
            case 'setcard':
                Card::setCardCache();//设置卡片
//                CardGoodsLive::setCardGoodCache();//设置卡片商品
                break;
            case 'getcard':
                $code = Card::getCardCache(0);
                echo $code;
                break;
            case 'setcardgood':
                CardGoodsLive::setCardGoodCache();//设置卡片商品
                break;
            case 'getcardgood':
                echo Cache::get('card_good_16');
                break;
            case 'getescardgood':
//                $ids = "1547,1541";
                $ids = "547";
                (new AdExternal())->getGoodListByids($ids);
                break;
            case 'getenvimage':
                var_dump(env("ALIURL"));
                break;

            case 'delcard':
                Cache::delete('card_index');
                Cache::delete('card_flash');
                break;
        }

        // 指令输出
        $output->writeln('cardCache');
    }
}
