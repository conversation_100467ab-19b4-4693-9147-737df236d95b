<?php
declare (strict_types = 1);

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;
use app\controller\v3\ElasticSearch;
use app\service\v3\Game;
use think\trace\TraceDebug;

class CleanCoupon extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('CleanCoupon')
            ->setDescription('自动清理商品已下架指定优惠券');
    }

    protected function execute(Input $input, Output $output)
    {
        $es = new ElasticSearch();
        $game_ser = new Game();
        $specify = Db::name('rotary_draw')->where([['name', 'like', '指定酒款%']])->select()->toArray();
        foreach ($specify as $val) {
            // 绑定优惠券
            $c = $val['number'];
            $c_a = explode(',', $c);
            if (!empty($c)) {
                $domain = env('ITEM.COUPON_URL').'/coupon/v3/coupon/getPeriodsByCouponIds?coupon_id='.$c;
                $data = get_url($domain);
                $result = json_decode((string)$data,true);
                // 查询优惠券指定期数状态
                if (!empty($result['data'])) {
                    foreach ($result['data'] as $key => &$v) {
                        $period = $es->getPeriodList($v['periods']);
                        // 期数下架或未查询到期数，删除优惠券
                        if (!empty($period)) {
                            if ($period[0]['onsale_status'] != 2) {
                                unset($result['data'][$key]);
                            }
                        } else {
                            unset($result['data'][$key]);
                        }
                    }
                    unset($key, $v);
                }
                // 优惠券期数全部下架，清空优惠券，中奖概率变更为0
                $time = time();
                if (empty($result['data'])) {
                    $game_ser->updateRotaryDraw([
                        'id' => $val['id'],
                        'number' => '',
                        'probability' => 0,
                        'updated_time' => $time
                    ]);
                } else {
                    // 还存在优惠券，更新优惠券，中奖概率不变
                    $new_c_a = [];
                    foreach ($result['data'] as $v) {
                        array_push($new_c_a, $v['id']);
                    }
                    if ($c_a != $new_c_a) {
                        $s_c_a = implode(',', $new_c_a);
                        $game_ser->updateRotaryDraw([
                            'id' => $val['id'],
                            'number' => $s_c_a,
                            'updated_time' => $time
                        ]);
                    }
                }
            }
        }
        print_r('执行完毕~');
    }
}
