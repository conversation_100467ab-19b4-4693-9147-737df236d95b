<?php
// 应用公共文件

use think\Response;
use think\facade\Log;

/**
 * 接口获取返回方法
 * <AUTHOR> <<EMAIL>>
 * @Date 2021/09/13 14:04
 * @param array $options 返回参数
 * @param int $code 状态码
 * @param string $error_msg 错误信息
 * @param array $header 返回头信息
 * @return Response
 */
if (!function_exists('throwResponse')) {
    function throwResponse($options = [], int $code = 0, string $error_msg = 'success', array $header = []): Response
    {
        $data['error_code']  = $code;
        $data['error_msg']  = $error_msg;
        $data['data'] = $options;
        return Response::create($data, 'json', 200)->header($header);
    }
}

/**
 * 自定义异常
 * <AUTHOR> <<EMAIL>>
 * @Date 2021/09/13 14:04
 * @param string $msg 错误信息
 * @param string $level 日志级别
 * @param int $code 状态码
 * @return Exception
 */
if (!function_exists('excep')) {
    function excep($msg = '', $level = 'info', $code = 10002) {
        throw new app\CustomException($msg, $level, $code);
    }
}

/**
 * curl请求
 * <AUTHOR> <<EMAIL>>
 * @Date 2021/09/13 14:04
 * @param string $url 请求地址
 * @param string $http 请求方式
 * @param array $data 请求数据
 * @param bool $cert 是否需要证书
 * @param int $timeout 超时时间
 * @return object
 */
if (!function_exists('httpCurl')) {
    function httpCurl($url, $http = 'get', $data= [], $timeout = 10) {
        $headers = [
            'Content-Type: application/json;charset=utf-8',
        ];
        
        $curl = curl_init(); //初始化
        curl_setopt($curl, CURLOPT_URL, $url); //设置抓取的url
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // 跳过证书检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false); // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_HEADER, false); //设置头文件的信息作为数据流输出
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers); //设置头信息
        if ($http == 'post') {
            curl_setopt($curl, CURLOPT_POST, true); //设置post方式提交
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data); //设置post数据
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true); //设置获取的信息以文件流的形式返回，而不是直接输出。
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout); //设置超时时间
        $response = curl_exec($curl); //执行命令
        curl_close($curl); //关闭URL请求
        // print_r($response); //显示获得的数据
        return $response;
    }
}

if (!function_exists('serviceReturn')) {
    /**
     * service 返回数据
     * <AUTHOR>
     * @param bool $status
     * @param string $data
     * @param string $msg
     * @return array
     */
    function serviceReturn(bool $status = true, $data = '', string $msg = ''): array
    {
        return ['status' => $status, 'data' => $data, 'msg' => $msg];
    }
}

if (!function_exists('post_url')) {
    /**
     * 模拟POST提交
     * <AUTHOR>
     * @param string $url 地址
     * @param array | string $data 提交的数据
     * @return string 返回结果
     */
    function post_url($url, $data, $header = [])
    {
        if (is_array($data)) {
            $data = http_build_query($data);
        }
        // 启动一个CURL会话
        $curl = curl_init();
        // 要访问的地址
        curl_setopt($curl, CURLOPT_URL, $url);
        // 对认证证书来源的检查
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        // 从证书中检查SSL加密算法是否存在
        curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        // 模拟用户使用的浏览器
        curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 5.01; Windows NT 5.0)');
        // 发送一个常规的Post请求
        curl_setopt($curl, CURLOPT_POST, 1);
        // Post提交的数据包
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        // 设置超时限制 防止死循环
        curl_setopt($curl, CURLOPT_TIMEOUT, 30);
        // 显示返回的Header区域内容
        curl_setopt($curl, CURLOPT_HEADER, 0);
        // 获取的信息以文件流的形式返回
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);

        $tmpInfo = curl_exec($curl); // 执行操作
//        if(curl_errno($curl))
//        {
//            echo 'Errno'.curl_error($curl);//捕抓异常
//        }
        curl_close($curl); // 关闭CURL会话
        return $tmpInfo; // 返回数据
    }
}

if (!function_exists('get_url')) {
    /**
     * 通过URL获取页面信息
     * @param $url 地址
     * @return mixed 返回页面信息
     */
    function get_url($url)
    {
        $ch = curl_init();
        //设置访问的url地址
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        //不输出内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}

if (!function_exists('get_interior_http_response')) {
    /**
     * 解析内部访问数据
     * <AUTHOR>
     * @param $json
     * @return mixed
     */
    function get_interior_http_response($json) {
        $result = json_decode($json, true);
        if ($result['error_code'] == '0') {
            return $result['data'];
        } else {
            return null;
        }
    }
}

if (!function_exists("pushMqlist")){

    function pushMqlist(string $exchange_name,string $routing_key,array $mqData){
        $client = new \GuzzleHttp\Client(['timeout' => 3]);
        $response = $client->post(env('ITEM.QUEUE_URL'),[
            'json' => [
                'exchange_name' => $exchange_name,
                'routing_key' => $routing_key,
                'data' => base64_encode(json_encode($mqData))
            ],
            'headers' => [
                'Content-Type'=>'application/json',
                "vinehoo-client" => "tp6-wine-wiki",//默认磐石系统
            ]
        ]);
        if($response->getStatusCode() !=200 ) {
            \think\Log::ERROR("队列同步失败".json_encode($mqData));
            excep("数据修改成功，队列同步失败");
        }
        return $response;
    }
}

if (!function_exists('esindex')){
    function esindex($params)
    {

        $es = new \app\ElasticSearchConnection();
        $client=$es->connection();
        $result = $client->search($params);
        if(!$result) excep("ES查询失败");
        if($result['timed_out'] == true) excep("ES查询超时");
        $resouce = &$result['hits'];
        $data  = $resouce['hits'];
//        echo json_encode($data);exit();
        if($data == []) return [];
        $resoucedata = array_column($data,'_source');
        return $resoucedata;
    }
}

if (!function_exists('get_es_result')) {
    /**
     * 解析 es 返回数据
     * @param array $es_data
     * @return array
     * <AUTHOR>
     */
    function get_es_result(array $es_data): array
    {
        $data['total'] = 0;
        $data['list'] = [];
        if ($es_data['hits']['total']['value'] < 1) {
            return $data;
        }
        $data['total'] = $es_data['hits']['total']['value'];
        foreach ($es_data['hits']['hits'] as $val) {
            $data['list'][] = $val['_source'];
        }
        return $data;
    }
}

if(!function_exists('adExposurelog')){

    /**
     * 曝光度
     * @param array $param
     * @return bool
     */
    function adExposurelog(array $param){

        $url = env('ITEM.COMMODITIES_URL').'/commodities/v3/other/exposureRate';
        $result = post_url($url,$param);
//        dump($url,$param,$result);
        if(is_string($result)){
            $result = json_decode($result,true);
        }
        if($result['error_code'] == 0){
            return true;
        }
        return false;
    }
}

/**
 * Description:curl请求
 * Author: zrc
 * Date: 2023/6/25
 * Time: 13:31
 * @param $url
 * @param array $data
 * @param array $haeder
 * @param string $method
 * @param int $timeout
 * @param bool $sync
 * @return mixed
 */
function curlRequest($url, $data = [], $haeder = [], $method = 'POST', $timeout = 30, $sync = True)
{
    if ($method == 'POST' && !is_array($data) && !in_array('Content-Type:application/json', $haeder)) {
        $haeder[] = 'Content-Type:application/json';
    }
    $ch = curl_init();
    if (is_array($data)) {
        $data = http_build_query($data);
    }
    if ($method == 'POST') {
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, True);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    } else {
        if (!empty($data)) curl_setopt($ch, CURLOPT_URL, "{$url}?{$data}");
        else curl_setopt($ch, CURLOPT_URL, $url);
    }
    if (strlen($url) > 5 && strtolower(substr($url, 0, 5)) == "https") {
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    }
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    if ($haeder) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $haeder);
    }
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, $sync);
    curl_setopt($ch, CURLOPT_HEADER, 0);
    curl_setopt($ch, CURLOPT_NOBODY, !$sync);
    $return = curl_exec($ch);
    curl_close($ch);
    #记录请求日志
    $param = is_array($data) ? json_encode($data) : $data;
    Log::info("请求URL：{$url}，请求参数：{$param}，响应参数：" . $return);
    return json_decode($return,true);
}

/**
 * 发送企业微信消息推送队列
 * @param array $content 消息内容
 * @param string $access_token 机器人token
 * @return array
 */
function SendWeChatRobot($content, $access_token)
{
    if (is_array($content)) {
        $content = json_encode($content, JSON_UNESCAPED_UNICODE);
    }
    $d_str = base64_encode(json_encode([
        'access_token' => $access_token,
        'type'         => 'text',
        'at'           => '',
        'content'      => base64_encode($content),
    ]));

    // 推送请求参数
    $push_data = [
        'exchange_name' => 'dingtalk',
        'routing_key'   => 'dingtalk_sender',
        'data'          => $d_str,
    ];

    $url_header = [
        'vinehoo-client: tp6-commodities',
        'Content-Type: application/json;charset=utf-8'
    ];
    return curlRequest(env('item.QUEUE_URL'), json_encode($push_data), $url_header);
}

/**
 * 把所有的在售商品按照规则自动添加商品到营销版块
 * @param array $column 栏目
 * @param array $card 卡片
 * @param array $label 标签
 * @return array
 */
function AutomaticallyAddAllPeriod($column, $card = [], $label = [])
{
    $url = env('ITEM.COMMODITIES_SERVICES') . '/commodities_server/v3/marketing/AutomaticallyAddAllPeriod';

    $res = curlRequest($url, json_encode([
        'column' => $column,
        'card' => $card,
        'label' => $label,
    ]));
    return $res;
}

/**
 * @方法描述:分页处理
 * <AUTHOR>
 * @param array $param 参数
 * @return array [分页起始值,返回条数]
 */
function pageHandle($param)
{
    $page  = $param['page'] ?? 1;
    $limit = $param['limit'] ?? 10;
    $page  = $page > 0 ? $page : 1;
    $limit = $limit > 0 ? $limit : 10;
    #分页起始值
    $offsetMain = ($page - 1) * $limit;

    return [intval($offsetMain), intval($limit)];
}


if (!function_exists('image_full_path')) {
    /**
     * @方法描述: 单图返回全路径
     * <AUTHOR>
     * @Date 2022/12/8 12:41
     * @param $value
     * @return string
     */
    function image_full_path($value)
    {
        if (!$value) return '';
        $host = env('ALIURL');
        if (empty($host)) return $value;
        return (false === strpos($value, $host)) ? $host . $value : $value;
    }
}
