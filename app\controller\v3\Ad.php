<?php

namespace app\controller\v3;

use app\BaseController;
use think\cache\driver\Redis;
use think\facade\Cache;
use think\Request;
use app\service\v3\Ad as AdService;

class Ad extends BaseController
{
    public $channel = [
        0 => "首页", 
        1 => "闪购", 
        2 => "秒发" ,
        3 => "社区" ,
        4 => "兔头",
        5 => "个人中心",
        6 => "商家秒发",
        7 => '烈酒',
        8 => '跨境',
        9 => '尾货',
    ];
    public $type = [
        1 => "banner", 
        2 => "开屏" ,
        3 => "弹窗" ,
        4 => "胶囊",
        5 => "广告位",
        7 => '胶囊新人广告',
        10 => 'banner',
    ];

    const MAX_AD_NUMBER = 6;

    public $options = [
        'host'       => '127.0.0.1',
        'port'       => 6379,
        'password'   => '',
        'select'     => 0,
        'timeout'    => 0,
        'expire'     => 0,
        'persistent' => false,
        'prefix'     => '',
        'tag_prefix' => 'tag:',
        'serialize'  => [],
    ];

    protected function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
        $this->options["host"]     = env("CACHE.HOST", "127.0.0.1");
        $this->options["port"]     = env("CACHE.PORT", "3306");
        $this->options["password"] = env("CACHE.PASSWORD", "");
        $this->options["prefix"]   = env("CACHE.prefix", "vinehoo");

    }

    public function list(Request $request)
    {
        $param        = $request->param();
        $param["uid"] = $request->header('vinehoo-uid');

        $response = AdService::list($param);
        return throwResponse($response);
    }

    /**
     * 创建
     * @param Request $request
     * @return \think\Response
     */
    public function create(Request $request)
    {
        $param                  = $request->param();
        $param['operator_id']   = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['created_at']    = time();
//        $param['status'] = 0;

        validate(\app\validate\Ad::class)->scene('create')->check($param);
        if (strtotime($param['start_time']) >= strtotime($param['end_time'])) return excep("上架时间必须小于下架时间");//时间验证
        $this->cheakTimeOnly($param);
        AdService::create($param);
        return throwResponse();
    }

    /**
     * 修改
     * @param Request $request
     * @return \think\Response
     */
    public function update(Request $request)
    {
        $param                  = $request->param();
        $param['operator_id']   = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Ad::class)->check($param);
        if (strtotime($param['start_time']) >= strtotime($param['end_time'])) return excep("上架时间必须小于下架时间");//时间验证
        $this->cheakTimeOnly($param, 2);

        AdService::update($param);
        return throwResponse();
    }

    /**
     * 核对不同类型的广告要求不同
     * @param $param
     * @return void
     * @throws \app\CustomException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function cheakTimeOnly($param, $method = 1)
    {
        //开屏广告 限制
        if ($param['type'] == 2 && $method == 2) {

            $start_time = strtotime($param['start_time']);
            $end_time   = strtotime($param['end_time']);
//            $sql = "select * from vh_ad where (start_time >".$start_time." and start_time <".$end_time.") or (start_time<".$start_time." and end_time >".$end_time.") or (end_time>".$start_time." and end_time<".$end_time.")";
//            $whereraw = "(start_time >=".$start_time." and start_time <=".$end_time.") or (start_time <=".$start_time." and end_time >=".$end_time.") or (end_time>=".$start_time." and end_time<=".$end_time.")";
            $whereraw = "(start_time >" . $start_time . " and start_time <" . $end_time . ") or (start_time <" . $start_time . " and end_time >" . $end_time . ") or (end_time>" . $start_time . " and end_time<" . $end_time . ")";
            $row      = \app\model\Ad::where("type", "=", 2)->where('status', '=', 1)->whereRaw($whereraw)->select();
            foreach ($row as $v) {
                if ($row) {
                    if (isset($param['id']) && $param['id'] == $v['id']) {

                    } else {
                        excep("与'" . $v['title'] . "'时间区间重复");
                    }

                }
            }

        }

        $channel = $this->channel;
        //banner位限制
        if (in_array($param['type'], [1, 4, 10]) && @$param['status'] == 1) {
            $p_type_name = $this->type[$param['type']] ?? '';

            $client_all = ['0' => 'IOS', '1' => '安卓', '2' => '小程序', '3' => 'h5', '4' => 'pc',];
            $hode_all   = $this->channel;

            $hode       = [];
            $client_arr = [];

            if (!empty($param['client'])) {
                foreach ($param['client'] as $cid) {
                    $client_arr[$cid] = $client_all[$cid] ?? $cid;
                }
            }

            if (!empty($param['channel'])) {
                foreach ($param['channel'] as $hid) {
                    $hode[$hid] = $hode_all[$hid] ?? $hid;
                }
            }
            $mid = $param['mid'] ?? 0;

            if (isset($param['id'])) {
                $channel = \app\model\Ad::field('channel,client,id,mid')->where("id", $param['id'])->find();
                $mid     = $channel['mid'] ?? 0;
                    $channel ?? excep("该数据不存在或频道为空。不允许修改");

                if (empty($param['channel'])) {
                    $hode = array_column($channel['channel'], 'name', 'id');
                }

                if (empty($param['client'])) {
                    $client_arr = array_column($channel['client'], 'name', 'id');
                }
            }

            $where = [
                ['type', '=', $param['type']],
                ['status', '=', 1],
            ];
            if ($mid != 0) {
                array_push($where, ['mid', '=', $mid]);
            }
            if (isset($param['id'])) {
                $where[] = ['id', '<>', $param['id']];
            }

            $count_list = \app\model\Ad::where($where)->column('channel,client,id,mid');
            $count_arr  = [];
            foreach ($count_list as $count_item) {
                $c_channel_item = explode(',', $count_item['channel']);
                $c_client_item  = explode(',', $count_item['client']);

                foreach ($c_channel_item as $ccli) {
                    foreach ($c_client_item as $ccti) {
                        $count_arr["{$ccli}_{$ccti}"][] = $count_item['id'];
                    }
                }
            }

            foreach ($hode as $chl_id => $chl_name) {
                foreach ($client_arr as $cli_id => $cli_name) {
                    if (count($count_arr["{$chl_id}_{$cli_id}"] ?? []) >= 6) excep("{$chl_name} {$cli_name} {$p_type_name}同时开启数量,同频道下不能超过6个");
                }
            }
        }

        //新人7天胶囊
        if ($param['type'] == 7 && $param['status'] == 1) {
            if (isset($param['id'])) {
                $status = \app\model\Ad::whereNotIN("id", $param['id'])->where('type', 7)->where('status', 1)->value('title');
                if ($status) excep($status . " 已开启，新人胶囊仅允许同时开启一个。");
            } else {
                $status = \app\model\Ad::where('status', 1)->where('type', 7)->value('title');
                if ($status) excep($status . " 已开启，新人胶囊仅允许同时开启一个。");
            }

        }

    }


    public function status(Request $request)
    {
        $param                  = $request->param();
        $param['operator_id']   = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Ad::class)->scene('status')->check($param);


        //banner位限制
        if (in_array($param['type'], [1, 4, 10]) && @$param['status'] == 1) {

            $p_type_name = $this->type[$param['type']] ?? '';

            $channel = \app\model\Ad::field('channel,client,id,mid')->where("id", $param['id'])->find();
            $mid     = $channel['mid'] ?? 0;
                $channel ?? excep("该数据不存在或频道为空。不允许修改");

            $hode       = array_column($channel['channel'], 'name', 'id');
            $client_arr = array_column($channel['client'], 'name', 'id');

            $where = [
                ['type', '=', $param['type']],
                ['status', '=', 1],
            ];
            if ($mid != 0) {
                array_push($where, ['mid', '=', $mid]);
            }
            $count_list = \app\model\Ad::where($where)->column('channel,client,id,mid');
            $count_arr  = [];
            foreach ($count_list as $count_item) {
                $c_channel_item = explode(',', $count_item['channel']);
                $c_client_item  = explode(',', $count_item['client']);

                foreach ($c_channel_item as $ccli) {
                    foreach ($c_client_item as $ccti) {
                        $count_arr["{$ccli}_{$ccti}"][] = $count_item['id'];
                    }
                }
            }

            foreach ($hode as $chl_id => $chl_name) {
                foreach ($client_arr as $cli_id => $cli_name) {
                    if (count($count_arr["{$chl_id}_{$cli_id}"] ?? []) >= 6) excep("{$chl_name} {$cli_name} {$p_type_name}同时开启数量,同频道下不能超过6个");
                }
            }
        } elseif (@$param['type'] == 3 && @$param['status'] == 1) {
            $pattern = \app\model\Ad::where("id", $param['id'])->value('pattern');
                $pattern ?? excep("该数据不存在或模式为空。不允许修改");
            $pattern_ids = explode(',', $pattern);

            $count = \app\model\Ad::where(function ($query) use ($pattern_ids) {
                foreach ($pattern_ids as $pattern_id) {
                    $query->whereOr(function ($query) use ($pattern_id) {
                        $query->whereFindInSet('pattern', $pattern_id);
                    });
                }
            })->where([
                ['id', '<>', $param['id']],
                ['type', '=', 3],
                ['status', '=', 1],
            ])->count();
            if ($count) excep("弹窗广告在同一模式下只能同时启用一个, 请检查后重试");
        } elseif (@$param['type'] == 2 && @$param['status'] == 1) {
            $res = \app\model\Ad::where("id", $param['id'])->find();
            if ($res) {
                $resarr = $res->toArray();
                $this->cheakTimeOnly($resarr, 2);
            }
        } elseif (@$param['type'] == 7 && @$param['status'] == 1) {
            $res = \app\model\Ad::where("id", $param['id'])->find();
            if ($res) {
                $resarr           = $res->toArray();
                $resarr['status'] = $param['status'];
                $this->cheakTimeOnly($resarr, 2);
            }

        }

        \app\model\Ad::where("id", $param['id'])->save($param);
        return throwResponse();
    }

    /**
     * 客户端过滤
     * @param Request $request
     * @return \think\Response
     */
    public function clientlist(Request $request)
    {
        $param                           = $request->param();
        $param["uid"]                    = $request->header('vinehoo-uid');
        $param['vinehoo_client_version'] = $request->header('vinehoo-client-version');
        $param['vine_client']            = $request->header('vinehoo-client');

        $response = AdService::clientlist($param);
        return throwResponse($response);
    }

    /**
     * 综合广告返回
     * @param Request $request
     * @return \think\Response
     */
    public function clientmergelist(Request $request)
    {
        $time                   = time();
        $param["uid"]           = $request->header('vinehoo-uid');
        $vinehoo_client_version = $request->header('vinehoo-client-version');
        $client                 = $request->param("client");

        $key_suffix = '';
        if (!empty($vinehoo_client_version) && isset($client) && in_array($client, [0, 1])) {
            // 当前 安卓版本号 9.0.7 IOS版本号 9.13 --2023年1月17日10:09:30
            if (($client == 0 && $vinehoo_client_version < 9.14) ||
                ($client == 1 && $vinehoo_client_version < 9.08)) {
                $key_suffix = '20230118';
            }
        }

        $openscreenArr = json_decode(Cache::get('vinehoo.ad.openscreen' . $key_suffix), true) ?? [];//开屏广告
        $popupArr      = json_decode(Cache::get('vinehoo.ad.popup' . $key_suffix), true) ?? [];//弹窗广告

        if ($client) {
            $openscreenArr = $this->clientmergedatalist($openscreenArr, $client);
            $popupArr      = $this->clientmergedatalist($popupArr, $client);
        }
        $paramuser   = ['uid' => $param["uid"]];//判断是否是新用户
        $newUser     = \app\service\v3\Common::chackNewUser($paramuser);//1 新用户 2老用户 3未登录
        $popupResult = [];

        foreach ($popupArr as $k => $v) {
            $sjf    = strpos($popupArr[$k]['image'], '?') ? "&" : "?";
            $extstr = strstr($popupArr[$k]['image'], "?", true) ?: $popupArr[$k]['image'];

            $w          = 450;
            $h          = 620;
            $ext        = strrev(strchr(strrev($extstr), '.', true));//后缀
            $v['image'] = $popupArr[$k]['image'] . $sjf . 'x-oss-process=image/resize,w_' . $w . ',h_' . $h . '/auto-orient,1/quality,q_90/format,' . $ext;
            //图片MD5
            $popupArr[$k]['image_md5'] = md5_file($v['image']);
            $pattern_ids               = explode(',', $v['pattern']);
            if (in_array($newUser, $pattern_ids)) {//是否新用户
                if (!empty($v['start_time']) && !empty($v['end_time'])) {
                    if (strtotime($v['start_time']) <= $time && strtotime($v['end_time']) > $time) {
                        array_push($popupResult, $v);
                    }
                }
            }
        }

        $openscreen_list = [];
        if (!empty($openscreenArr)) {
            foreach ($openscreenArr as &$v) {
                //图片MD5
                $v['image_md5'] = md5_file($v['image']);
                // 验证开始时间
                if (!empty($v['start_time']) && !empty($v['end_time'])) {
                    if (strtotime($v['start_time']) <= $time && strtotime($v['end_time']) > $time) {
                        $openscreen_list[] = $v;
                    }
                }
            }
        }

        $resouceArr = ['openscreen' => $openscreenArr, 'popup' => $popupResult, 'current_time' => date("Y-m-d H:i:s", time())];
        return throwResponse($resouceArr);
    }

    public function clientmergedatalist($data, $client)
    {

        $openscreenArr = [];
        foreach ($data as $value) {

            $data = $value;
            if ($client == 9) {
                array_push($openscreenArr, $data);
            } else {
                $keys = array_keys($data['client']);
//                var_dump($keys);
                if (in_array($client, $keys)) {
                    array_push($openscreenArr, $data);
                }
            }
        }
        return $openscreenArr;
    }

    /**
     * 获取开屏广告
     * @param $client
     * @return array
     */
    public function getOpenscreenData($client)
    {
        //开屏广告
        $redis = new Redis($this->options);

        $len           = $redis->LLEN('vinehoo.ad.openscreen');
        $openscreen    = $redis->LRANGE("vinehoo.ad.openscreen", 0, $len);
        $openscreenArr = [];
        foreach ($openscreen as $value) {
            $data = json_decode($value, true);
            if ($client == 9) {
                array_push($openscreenArr, $data);
            } else {
                $keys = array_keys($data['client']);
//                var_dump($keys);
                if (in_array($client, $keys)) {
                    array_push($openscreenArr, $data);
                }
            }
        }

        return $openscreenArr;
    }

    /**
     * 获取弹窗广告
     * @param $client
     * @return array
     */
    public function getPopupData($client)
    {
        //弹窗广告
        $redis    = new Redis($this->options);
        $len      = $redis->LLEN('vinehoo.ad.popup');
        $popup    = $redis->LRANGE("vinehoo.ad.popup", 0, $len);
        $popupArr = [];
        $popup    = json_decode(Cache::get("vinehoo.ad.popup"), true);
//        return $popup;
        foreach ($popup as $value) {
//            $data = json_decode($value,true);
            $data = $value;
//            var_dump($data);exit();
            if ($client == 9) {
                array_push($popupArr, $data);
            } else {
                $keys = array_keys($data['client']);
                if (in_array($client, $keys)) {
                    array_push($popupArr, $data);
                }
            }
        }
        return $popupArr;
    }

    /**
     * 全量缓存更新
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function cacheCreate()
    {
        $where    = function ($query) {
            $query->where('status', 1);
            //上架时间判断
            $nowtime = time();
            $query->where('end_time', ">=", $nowtime);
        };
        $whereraw = "type=2 or type=3";
        $Ad       = \app\model\Ad::where($where)->whereRaw($whereraw)->order('sort', 'desc')->select()->toArray();

        foreach ($Ad as $value) {
            \app\service\v3\Ad::setAdCache($value['id']);
        }
    }

    /**
     * @方法描述: 根据商品上下架, 关联广告自动上下架
     * <AUTHOR>
     * @Date 2022/8/16 14:38
     * @param Request $request
     * @return \think\Response
     * @throws \app\CustomException
     */
    public function syncGoodsStatus(Request $request)
    {
        #region 验证参数
        $validate = \think\facade\Validate::rule([
            'table|表名'             => 'require',
            'period|期数'            => 'require|number|gt:0',
            'onsale_status|商品状态' => 'require|number'
        ]);
        $param    = $request->param();
        if (!$validate->check($param)) {
            excep($validate->getError());
        }
        #endregion 验证参数

        \app\service\v3\Ad::syncGoodsStatus($param);
        return throwResponse();
    }

    public function detail(Request $request)
    {
        $param = $request->param();
        if (!isset($param['id']) || strlen($param['id']) <= 0) {
            excep("数据不存在，请检查id");
        }
        $response = AdService::detail($param);
        return throwResponse($response);
    }


    public function prescription(Request $request)
    {
        $param        = $request->param();
        $param["uid"] = $request->header('vinehoo-uid');
        $result       = \app\service\v3\Ad::prescription($param);
        return throwResponse($result);
    }
}