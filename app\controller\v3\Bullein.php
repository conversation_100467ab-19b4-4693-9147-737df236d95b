<?php

namespace app\controller\v3;

use think\Request;
use app\service\v3\Bullein as BulleinService;

class Bullein
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        
        $response = BulleinService::list($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Bullein::class)->scene('bullein/form')->check($param);
        BulleinService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Bullein::class)->scene('bullein/form')->check($param);
        
        BulleinService::update($param);
        return throwResponse();
    }

    /**
     * 删除
     * @param  \think\Request  $request
     * @return array
     */
    public function delete(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\Bullein::class)->scene('bullein/login')->check($param);

        BulleinService::delete($param);
        return throwResponse();
    }

    /**
     * 更改状态
     * @param  \think\Request  $request
     * @return array
     */
    public function status(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\Bullein::class)->scene('bullein/is_show')->check($param);
        
        BulleinService::update($param);
        return throwResponse();
    }

    public function detail(Request $request) {
        $param = $request->param();

        if(!isset($param['id'])) excep('快报id为必填参数');

        $result = BulleinService::detail($param);
        return throwResponse($result);
    }
    public function clientdetail(Request $request) {
        $param = $request->param();

        if(!isset($param['id'])) excep('快报id为必填参数');

        $result = BulleinService::clientdetail($param);
        return throwResponse($result);
    }

    /**
     * 获取选中快报
     * @param  \think\Request  $request
     * @return array
     */
    public function getChecked(Request $request) {
        $param = $request->param();

        $response = BulleinService::getChecked($param);
        return throwResponse($response);
    }

    public function clientlist(Request $request) {
        $param = $request->param();

        $response = BulleinService::clientlist($param);
        return throwResponse($response);
    }
}
