<?php

namespace app\controller\v3;

use think\facade\Cache;
use think\Request;
use app\service\v3\Card as CardService;



class Card
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();

        $response = CardService::list($param);

        return throwResponse($response);
    }

    /**
     * 详情
     * @param  \think\Request  $request
     * @return array
     */
    public function detail(Request $request) {
        $param = $request->param();
        validate(\app\validate\Card::class)->scene('card/detail')->check($param);

        $response = CardService::detail($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['created_at'] = time();

        validate(\app\validate\Card::class)->scene('card/create')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }
        CardService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Card::class)->scene('card/update')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }
        CardService::update($param);
        return throwResponse();
    }

    /**
     * 更改状态
     * @param  \think\Request  $request
     * @return array
     */
    public function status(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Card::class)->scene('card/status')->check($param);

        CardService::updateStatus($param);
        return throwResponse();
    }

    public function delect(Request $request)
    {
        $param = $request->param();
        validate(\app\validate\Card::class)->scene('card/delect')->check($param);
        CardService::delect($param);
        return throwResponse();
    }

    public function clientlist(Request $request)
    {
        $param = $request->param();
        $client = $request->header('vinehoo-client');
        if(isset($param['channel']) && strlen($param['channel']) > 0 && in_array($param['channel'],[0,2])){
            if(isset($param['mid']) && !is_array($param['mid']) && strlen($param['mid'])>0){
                    //获取商家数据

                    $redis = Cache::handler();
                    //商家卡片缓存
                    if($param['channel'] == 2){
                        //秒发
                        $rediskeys = 'vinehoo.card.merchant.flash';
                        $code = [];
                        $midarr = explode(',',$param['mid']);
                        foreach($midarr as $v){
                            $result = $redis->hget($rediskeys,$v);
                            if($result == null) continue;
                            $arrdata = (json_decode($result,true)['list']);
                            if(count($arrdata)>0){
                                $code = array_merge($code,$arrdata);break;
                            }
                            // echo json_encode($arrdata);
//                            $code = array_merge($code,$arrdata);break;
                        }
                        
                    
                        if($code != []){
                            return throwResponse(['list'=>$code,'total'=>count($code)]);
                        }
                            
                    }else if($param['channel'] == 0){
                        //首页
                        $rediskeys = 'vinehoo.card.merchant.index';
                        $code = [];
                        $midarr = explode(',',$param['mid']);
                        foreach($midarr as $v){
                            $result = $redis->hget($rediskeys,$v);
                            if($result == null) continue;
                            $arrdata = (json_decode($result,true)['list']);
                            if(count($arrdata)>0){
                                $code = array_merge($code,$arrdata);break;
                            }
                        }
                        
                        if($code != []) return throwResponse(['list'=>$code,'total'=>count($code)]);
                            
                    }

                
                }
            $code = CardService::getCardCache($param['channel']);
            if(!$code) return throwResponse([],0,"暂无缓存该数据");
            $arr = ['error_code'=>0,"error_msg"=>""];

            $row = json_decode($code,true);
            if($row){

                $data = $row['list'];
                if($client != "vinehoo-pc"){
                    foreach ($data as $k=>$v){

//                    var_dump($v);exit();
                        switch ($v['style']){
                            case 0://252*156

                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 252; $h=156;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 1://252*156
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 252; $h=156;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 2://180*180
                                // 秒发样式3
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 180; $h=180;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 3://128*142
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 128; $h=142;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 4://72*148
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 148; $h=148;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 5://72*148
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 148; $h=148;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 6://120*120
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 120; $h=120;//尺寸
                                    $sjf = strpos($value['product_img'],'?')?"&":"?";
                                    $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                    $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                    $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                    $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                    $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;
                            case 7://298*148
                                $card_extend_detail =  $v['card_extend_detail'];
                                foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                    $w = 298; $h=148;//尺寸
                                    $sjf = strpos($value['cover_url'],'?')?"&":"?";
                                    $extstr = strstr($value['cover_url'],"?",true)?:$value['cover_url'];
                                    $ext = strrev(strchr(strrev($extstr),'.',true));//后缀

                                    $card_extend_detail[$key]['cover_url'] =  $value['cover_url'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                    $card_extend_detail[$key]['cover_url']  =  $value['cover_url'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                }
                                $data[$k]['card_extend_detail']=$card_extend_detail;
                                break;

                        }
                    }
                }
                $arr['data']['list'] = $data;
            }else{
                $arr['data'] = [];
            }
//            $arr['data'] = $row??[];
            return json($arr);

        }else{
            return throwResponse([],-1,"频道参数不正确");
        }
        exit();
        $response = CardService::clientlist($param);
        return throwResponse($response);

    }

    public function mf_clientlist(Request $request)
    {
        $param = $request->param();
        $client = $request->header('vinehoo-client');
        $param['channel'] = 2;

        $code = CardService::getCardCache($param['channel']);
        if(!$code) return throwResponse([],0,"暂无数据");
        $arr = ['error_code'=>0,"error_msg"=>""];

        $row = json_decode($code,true);
        if($row){
            $data = $row['list'];
            if($client != "vinehoo-pc"){
                foreach ($data as $k=>$v) {
                    $card_extend_detail =  $v['card_extend_detail'];
                    if ($param['channel'] == 2) {
                        // 取前3条数据
                        $card_goods = [];
                        foreach ($card_extend_detail as $k1 => $v1) {
                            if (!empty($v1['card_filter_info'])) {
                                $card_goods[] = $v1;
                            }
                        }
                        // 随机排序
                        shuffle($card_goods);
                        foreach ($card_goods as $k1 => $v1) {
                            if ($k1 >= 3) {
                                unset($card_goods[$k1]);
                            }
                        }
                        $card_extend_detail = $card_goods;
                    }
                    #不显示无商品卡片
                    if (empty($card_extend_detail)) {
                        unset($data[$k]);
                        continue;
                    }

//                    var_dump($v);exit();
                    switch ($v['style']){
                        case 0://252*156
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 252; $h=156;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 1://252*156
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 252; $h=156;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 2://180*180
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 180; $h=180;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 3://128*142
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 128; $h=142;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 4://72*148
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 148; $h=148;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 5://72*148
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 148; $h=148;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 6://120*120
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 120; $h=120;//尺寸
                                $sjf = strpos($value['product_img'],'?')?"&":"?";
                                $extstr = strstr($value['product_img'],"?",true)?:$value['product_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                                $card_extend_detail[$key]['product_img'] =  $value['product_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;

                                $sjf = strpos($value['banner_img'],'?')?"&":"?";
                                $extstr = strstr($value['banner_img'],"?",true)?:$value['banner_img'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀s
                                $card_extend_detail[$key]['banner_img']  =  $value['banner_img'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;
                        case 7://298*148
                            foreach ($card_extend_detail as $key=>$value){//图片尺寸限定 横向滑动
                                $w = 298; $h=148;//尺寸
                                $sjf = strpos($value['cover_url'],'?')?"&":"?";
                                $extstr = strstr($value['cover_url'],"?",true)?:$value['cover_url'];
                                $ext = strrev(strchr(strrev($extstr),'.',true));//后缀

                                $card_extend_detail[$key]['cover_url'] =  $value['cover_url'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                                $card_extend_detail[$key]['cover_url']  =  $value['cover_url'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                            }
                            $data[$k]['card_extend_detail']=$card_extend_detail;
                            break;

                    }
                }
            }
            $arr['data']['list'] = array_values($data);
        }else{
            $arr['data'] = [];
        }
        return json($arr);

    
        exit();
        $response = CardService::clientlist($param);
        return throwResponse($response);

    }

    /**
     * 卡片子项列表
     * @param  \think\Request  $request
     * @return array
     */
    public function cardfilterlist(Request $request) {
        $param = $request->param();
        validate(\app\validate\Card::class)->scene('cardfilterlist')->check($param);
        $response = CardService::cardfilterlist($param);
        return throwResponse($response);
    }

    /**
     * 添加卡片子项
     * @param  \think\Request  $request
     * @return array
     */
    public function cardfilteradd(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Card::class)->scene('cardfilteradd')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }

        CardService::cardfilteradd($param);
        return throwResponse();
    }

    /**
     * 编辑卡片子项
     * @param  \think\Request  $request
     * @return array
     */
    public function cardfilteredit(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Card::class)->scene('cardfilteredit')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }

        CardService::cardfilteredit($param);
        return throwResponse();
    }

    /**
     * 删除卡片子项
     * @param  \think\Request  $request
     * @return array
     */
    public function cardfilterdel(Request $request) {
        $param = $request->param();
        validate(\app\validate\Card::class)->scene('cardfilterdel')->check($param);
        CardService::cardfilterdel($param);
        return throwResponse();
    }

    /**
     * 前端商品列表接口（根据page_mode返回不同格式）
     * @param  \think\Request  $request
     * @return array
     */
    public function clientgoodslist(Request $request) {
        $param = $request->param();
        validate(\app\validate\Card::class)->scene('clientgoodslist')->check($param);
        $response = CardService::clientgoodslist($param);
        return throwResponse($response);
    }

    /**
     * 商品列表
     * @param  \think\Request  $request
     * @return array
     */
    public function goodslist(Request $request) {
        $param = $request->param();
        $param['vinehoo_client'] = $request->header('vinehoo-client');

        $response = CardService::goodslist($param);
        return throwResponse($response);
    }
}
