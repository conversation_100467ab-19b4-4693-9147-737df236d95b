<?php

namespace app\controller\v3;

use app\model\Card;
use app\model\CardGoodsLive as CardGoodsLiveModel;
use app\service\v3\AdExternal;
use app\service\v3\CardGoodsLive as CardGoodsLiveService;
use think\facade\Cache;
use think\Request;

class CardGoodsLive
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        validate(\app\validate\CardGoodsLive::class)->scene('cardgoodslive/list')->check($param);

        $response = CardGoodsLiveService::list($param);
        return throwResponse($response);
    }

    public function clientgoodslist(Request $request) {
        $param = $request->param();
        validate(\app\validate\CardGoodsLive::class)->scene('cardgoodslive/list')->check($param);
        if(!isset($param['cid'])) excep("卡片不存在，请核对。");
        $page= $param['page'] ?? 1;
        $limit=$param['limit'] ?? 10;
        $fid = $param['fid'] ?? 0;
        $response = $this->getCaradGoods($param['cid'],$fid,$page,$limit);
        return throwResponse($response);

        $response = CardGoodsLiveService::clientgoodslist($param);
//        return throwResponse($response);
    }

    public function getCaradGoods($cid,$fid,$page,$limit)
    {
        $cacheresult = Cache::get('card_good_'.$cid);
//        var_dump($cacheresult);
        if($cid){
            $data =  json_decode($cacheresult,true);
            if(!$data)return [];

            //筛选项匹配
            if (!empty($fid)) {
                $list = [];
                foreach ($data['list'] as $v) {
                    if (!empty($v['card_filter'])) {
                        // 由于现在每个商品只有一条筛选项记录，直接检查第一条即可
                        if (isset($v['card_filter'][0]) && $v['card_filter'][0]['id'] == $fid) {
                            $list[] = $v;
                        }
                    }
                }
                $data['list'] = $list;  // 更新筛选后的列表
                $data['total'] = count($list);
            }

            $startindex = ($page-1)*$limit;
            $row = array_slice($data['list'],$startindex,$limit);
            $card_info = $data['card_info'] ?? [];

            // $goods_sort_type = Card::where('id', $cid)->value('goods_sort_type') ?? 0;
            $goods_sort_type = $card_info['goods_sort_type'] ?? 0;
            if (!empty($row)) {
                $data_list      = $row;
                // $list_periods   = array_column($data_list, 'id');
                $sort           = $data['goods_sort'] ?? [];
                // $sort           = CardGoodsLiveModel::where('relation_id', 'in', $list_periods)->where('cid', $cid)->order(['sort' => 'desc', 'created_at' => 'desc',])->column('sort', 'relation_id');
                $sort_data_list = [];
                foreach ($data_list as $item) {
                    $sort_data_list[($sort[$item['id']] ?? 0)][] = $item;
                }

                $sort_list = [];
                foreach ($sort_data_list as $sort_group) {
                    if ($goods_sort_type == 1) {
                        usort($sort_group, function ($a, $b) {
                            $at = empty($a['onsale_time']) ? 0 : strtotime($a['onsale_time']);
                            $bt = empty($b['onsale_time']) ? 0 : strtotime($b['onsale_time']);
                            return $bt - $at;
                        });
                    } elseif ($goods_sort_type == 2) {
                        shuffle($sort_group);
                    }
                    foreach ($sort_group as $si) {
                        $sort_list[] = $si;
                    }
                    $row = $sort_list;
                }
            }

            $p_share_image = "";
            if (empty($card_info['share_image'])) {
                if (!empty($row[0]['product_img'])) {
                    $p_share_image = explode(',', $row[0]['product_img'])[0] ?? '';
                }
            } else {
                $p_share_image = $card_info['share_image'] ?? '';
            }

            $result = ['list'=>$row,'total'=>$data['total'],'title'=>$data['title'],'card_info' => [
                'main_title' => empty($card_info['main_title']) ? (empty($card_info['card_name']) ? "" : $card_info['card_name']) : $card_info['main_title'],
                'secondary_title' => $card_info['secondary_title'] ?? '',
                'share_image' => $p_share_image,
                'share_url' => $card_info['share_url'] ?? '',
            ]];
            return $result;
        }
        return [];
    }

    public function filtergoodslist(Request $request) {
        $param = $request->param();
        if(!isset($param['cid'])) excep("卡片不存在，请核对。");
        $cid = $param['cid'];
        $cacheresult = Cache::get('card_good_' . $cid);

        $response = [];
        if (!empty($cacheresult)) {
            $data =  json_decode($cacheresult,true);

            if(empty($data))return throwResponse($response);

            $row = $data['list'] ?? [];
            $card_filter = $data['card_filter'] ?? [];
            $card_info = $data['card_info'] ?? [];
            $goods_sort_type = $card_info['goods_sort_type'] ?? 0;
            // $goods_sort_type = Card::where('id', $cid)->value('goods_sort_type') ?? 0;
            if (!empty($row)) {
                $data_list      = $row;
                // $list_periods   = array_column($data_list, 'id');
                $sort           = $data['goods_sort'] ?? [];
                // $sort           = CardGoodsLiveModel::where('relation_id', 'in', $list_periods)->where('cid', $cid)->order(['sort' => 'desc', 'created_at' => 'desc',])->column('sort', 'relation_id');
                $sort_data_list = [];
                foreach ($data_list as $item) {
                    $sort_data_list[($sort[$item['id']] ?? 0)][] = $item;
                }

                $sort_list = [];
                foreach ($sort_data_list as $sort_group) {
                    if ($goods_sort_type == 1) {
                        usort($sort_group, function ($a, $b) {
                            $at = empty($a['onsale_time']) ? 0 : strtotime($a['onsale_time']);
                            $bt = empty($b['onsale_time']) ? 0 : strtotime($b['onsale_time']);
                            return $bt - $at;
                        });
                    } elseif ($goods_sort_type == 2) {
                        shuffle($sort_group);
                    }
                    foreach ($sort_group as $si) {
                        $sort_list[] = $si;
                    }
                    $row = $sort_list;
                }
            }

            $list = [];
            foreach ($card_filter as $cf) {
                $cf['goods'] = [];
                foreach ($row as $v) {
                    if (empty($v['card_filter'])) {
                        continue;
                    }
                    // 由于现在每个商品只有一条筛选项记录，直接检查第一条即可
                    if (isset($v['card_filter'][0]) && $v['card_filter'][0]['id'] == $cf['id']) {
                        $cf['goods'][] = $v;
                    }
                }
                if (!empty($cf['goods'])) {
                    $list[] = $cf;
                }
            }

            $response = ['list'=>$list,'total'=>$data['total'],'title'=>$data['title'],'card_info' => [
                'main_title' => $card_info['main_title'] ?? '',
                'secondary_title' => $card_info['secondary_title'] ?? '',
                'share_image' => $card_info['share_image'] ?? '',
                'share_url' => $card_info['share_url'] ?? '',
            ]];
        }
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\CardGoodsLive::class)->scene('cardgoodslive/form')->check($param);

        if($param['type'] == 1)$this->checkPeriodsType($param['relation_id'],$param['cid']);//验证商品类型
        if($param['type'] == 2)$this->checkLiveStatus($param['relation_id']);//验证直播
        //验证直播、或者商品是否存在 本地数据库
        $this->checkGoodLiveDataIsset($param);//exit();
        CardGoodsLiveService::create($param);
        return throwResponse();
    }


    /**
     * 核对商品直播数据是否存在
     * @param $goodid
     * @param $id
     * @return bool
     */
    public function checkGoodLiveDataIsset($params)
    {
        $where = [['relation_id','=',$params['relation_id']],['cid','=',$params['cid']]];
        $result = CardGoodsLiveModel::where($where)->find();
        if($result) excep("数据已存在，不可重复绑定",'info',10103);//可以添加通过
    }

    /**
     * 商品数据验证
     * @param $id
     * @return void
     * @throws \app\CustomException
     */
    public function checkPeriodsType($id,$cid)
    {
        $restresult = AdExternal::getGoodDetail($id);//periods_type  0：闪购，1：秒发，2：跨境，3：尾货，4：兔头实物，5：兔头优惠券
        if(!$restresult) excep("商品不存在！");

        if($restresult['is_delete'] == 1)excep("商品已删除");
        if($restresult['onsale_status'] == 2 || $restresult['onsale_status'] == 1){

        }else{
            excep("商品不是在售中或者待售中");
        }
        if($restresult['periods_type'] == 4 || $restresult['periods_type'] == 5)excep("不可绑定兔头商品");
        if($restresult['is_channel'] == 1)excep("不可绑定渠道商品");
        $row = $this->getcard($cid);
        if(!$row) excep('卡片不存在');
        if($row['channel'] == 2){//秒发
            // if(!($restresult['periods_type'] == 1 || $restresult['periods_type'] == 9)) excep('该卡片为秒发频道，不能绑定其它频道商品');
        }
    }

    public function getcard($id)
    {
        $row = Card::where('id',$id)->find();
        return $row;
    }

    /**
     * 直播数据验证
     * @param $id
     * @return void
     * @throws \app\CustomException
     */
    public function checkLiveStatus($id)
    {
        $response = json_decode(httpCurl(env('ITEM.LIVE_URL') . '/live/v3/live/detail?live_id=' . $id), true);
        if(!$response)excep("直播接口访问异常");
        if ($response['error_code'] != 0) excep($response['error_msg']);
        if ($response['data']['status'] == 4) excep("直播不存在");
    }
    /**
     * 删除
     * @param  \think\Request  $request
     * @return array
     */
    public function delete(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\CardGoodsLive::class)->scene('cardgoodslive/login')->check($param);

        CardGoodsLiveService::delete($param);
        return throwResponse();
    }

    public function update(Request $request)
    {
        $param = $request->param();
        $param = $request->only(['id','title', 'sub_title','sort', 'image', 'filter_id'],$param);
//        var_dump($param);exit();
//        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!isset($param['id'])) excep('请传入唯一标识');
        if(!isset($param['sort'])) excep('排序为必传参数');
        $result = CardGoodsLiveService::update($param);
        return throwResponse([],0,"修改成功");
    }


    /**
     * 筛选列表
     * @param  \think\Request  $request
     * @return array
     */
    public function getfilter(Request $request)
    {
        $param = $request->param();
        if(!isset($param['id'])) excep('请传入唯一标识');
        
        $result = CardGoodsLiveService::getfilter($param);
        return throwResponse($result);
    }
}
