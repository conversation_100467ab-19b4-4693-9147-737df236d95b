<?php

namespace app\controller\v3;

use app\service\v3\ClientPathExtends;
use think\Request;
use app\service\v3\ClientPath as ClientPathService;

class ClientPath
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        
        $response = ClientPathService::list($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();

        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['created_at'] = time();


        validate(\app\validate\ClientPath::class)->scene('clientpath/form')->check($param);
        
        ClientPathService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\ClientPath::class)->scene('clientpath/form')->check($param);
        
        ClientPathService::update($param);
        return throwResponse();
    }

    public function filterlist(Request $request)
    {
        $param = $request->param();
        if(!isset($param['pid']) || strlen($param['pid'])<=0){
            excep("pid不能为空");
        }
        $data = ClientPathExtends::filterlist($param);
        return throwResponse($data);
    }

    public function codefilterlist(Request $request)
    {
        $param = $request->param('code');
        if(!isset($param) || strlen($param)<=0){
            excep("code唯一表示不能为空");
        }

        $data = ClientPathService::codefilterlist($param);
        return throwResponse($data);
    }

    public function codefiltermorelist(Request $request)
    {
        $param = $request->param('code');
        if(!isset($param) ){//|| count($param)<=0 ||!is_array($param)
            excep("code参数必须存在，请检查");
        }

        $data = ClientPathService::codefiltermorelist($param);
        return throwResponse($data);
    }

    public function multiplebyids(Request $request)
    {
        $param = $request->param('ids');
        if(!isset($param) ){//|| count($param)<=0 ||!is_array($param)
            excep("id参数必须存在，请检查");
        }

        $data = ClientPathService::multiplebyids($param);
        return throwResponse($data);

    }
}
