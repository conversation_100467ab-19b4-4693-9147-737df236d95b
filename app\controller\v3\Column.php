<?php

namespace app\controller\v3;

use think\facade\Cache;
use think\Request;
use app\service\v3\Column as ColumnService;



class Column
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();

        $response = ColumnService::list($param);

        return throwResponse($response);
    }

    public function detail(Request $request)
    {
        $param = $request->param();
        if(!isset($param['id']) || strlen($param['id'])<=0){
            excep("数据不存在，请检查id");
        }
        $response = ColumnService::detail($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Column::class)->scene('column/create')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }

        ColumnService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Column::class)->scene('column/update')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }
        ColumnService::update($param);
        return throwResponse();
    }

    /**
     * 更改状态
     * @param  \think\Request  $request
     * @return array
     */
    public function status(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Column::class)->scene('column/status')->check($param);

        ColumnService::updateStatus($param);
        return throwResponse();
    }

    public function delect(Request $request)
    {
        $param = $request->param();
        validate(\app\validate\Card::class)->scene('card/delect')->check($param);
        ColumnService::delect($param);
        return throwResponse();
    }

    public function clientlist(Request $request)
    {
        $param = $request->param();
        $param['vinehoo_client'] = $request->header('vinehoo-client');
        
        $response = ColumnService::clientlist($param);
        return throwResponse($response);

    }

    public function clientgoodslist(Request $request)
    {
        $param = $request->param();
        $param['vinehoo_client'] = $request->header('vinehoo-client');
        
        $response = ColumnService::clientgoodslist($param);
        return throwResponse($response);

    }

    /**
     * 筛选项商品列表
     * @param  \think\Request  $request
     * @return array
     */
    public function filtergoodslist(Request $request)
    {
        $param = $request->param();
        $param['vinehoo_client'] = $request->header('vinehoo-client');
        validate(\app\validate\Column::class)->scene('filtergoodslist')->check($param);

        $response = ColumnService::filtergoodslist($param);
        return throwResponse($response);

    }

    /**
     * 烈酒产品列表
     * @param  \think\Request  $request
     * @return array
     */
    public function goodslist(Request $request)
    {
        $param = $request->param();
        $param['vinehoo_client'] = $request->header('vinehoo-client');
        
        $response = ColumnService::goodslist($param);
        return throwResponse($response);

    }

    /**
     * 栏目子项列表
     * @param  \think\Request  $request
     * @return array
     */
    public function columnfilterlist(Request $request) {
        $param = $request->param();

        validate(\app\validate\Column::class)->scene('columnfilterlist')->check($param);

        $response = ColumnService::columnfilterlist($param);

        return throwResponse($response);
    }

    /**
     * 添加栏目子项
     * @param  \think\Request  $request
     * @return array
     */
    public function columnfilteradd(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Column::class)->scene('columnfilteradd')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }

        ColumnService::columnfilteradd($param);
        return throwResponse();
    }

    /**
     * 编辑栏目子项
     * @param  \think\Request  $request
     * @return array
     */
    public function columnfilteredit(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Column::class)->scene('columnfilteredit')->check($param);
        // 自动添加商品
        if (!empty($param['add_method']) && $param['add_method'] == 1) {
            if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                excep('请选择自动添加类型');
            }
            if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                excep('请选择定义内容');
            }
            foreach ($param['auto_add_content'] as $v) {
                if (!isset($v['id']) || !isset($v['name'])) {
                    excep('定义内容格式错误');
                }
            }
        }

        ColumnService::columnfilteredit($param);
        return throwResponse();
    }


    /**
     * 删除栏目子项
     * @param  \think\Request  $request
     * @return array
     */
    public function columnfilterdel(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Column::class)->scene('columnfilterdel')->check($param);

        ColumnService::columnfilterdel($param);
        return throwResponse();
    }
}
