<?php

namespace app\controller\v3;

use app\model\ColumnGoods as ColumnGoodsModel;
use app\service\v3\AdExternal;
use think\facade\Cache;
use think\facade\Db;
use think\Request;
use app\service\v3\ColumnGoods as ColumnGoodsService;
use \app\model\Column;
class ColumnGoods
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        validate(\app\validate\ColumnGoods::class)->scene('list')->check($param);

        $response = ColumnGoodsService::list($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\ColumnGoods::class)->scene('create')->check($param);
        ColumnGoodsService::create($param);
        return throwResponse();
    }

    public function update(Request $request)
    {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\ColumnGoods::class)->scene('update')->check($param);
        $result = ColumnGoodsService::update($param);
        return throwResponse([],0,"修改成功，受影响行数".$result);
    }

    /**
     * 删除
     * @param  \think\Request  $request
     * @return array
     */
    public function delete(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');

        validate(\app\validate\ColumnGoods::class)->scene('login')->check($param);

        ColumnGoodsService::delete($param);
        return throwResponse();
    }

    /**
     * 筛选列表
     * @param  \think\Request  $request
     * @return array
     */
    public function getfilter(Request $request)
    {
        $param = $request->param();
        if (empty($param['id']) && !empty($param['cid'])) {
            $param['id'] = $param['cid'];
        }
        if(empty($param['id'])) excep('请传入卡片ID');

        $result = ColumnGoodsService::getfilter($param);
        if (!empty($param['query_type']) && $param['query_type'] == 1) {
            $result[] = [
                'id' => -1,
                'name' => '无'
            ];
        }
        return throwResponse($result);
    }


    /**
     * 筛选项商品列表
     * @param  \think\Request  $request
     * @return array
     */
    public function getfiltergoodslist(Request $request)
    {
        $param = $request->param();
        $param['vinehoo_client'] = $request->header('vinehoo-client');
        validate(\app\validate\ColumnGoods::class)->scene('getfiltergoodslist')->check($param);

        $response = ColumnGoodsService::getfiltergoodslist($param);
        return throwResponse($response);

    }
}
