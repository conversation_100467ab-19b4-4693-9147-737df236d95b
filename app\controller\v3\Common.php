<?php

namespace app\controller\v3;

use app\service\v3\Common as CommonService;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\Request;


class Common
{
   /**
     * 频道
     * @param  \think\Request  $request
     * @return array
     */
    public function channel(Request $request) {
        $param = $request->param();

        $response = CommonService::channel($param);
        return throwResponse($response);
    }

    public function adconfig()
    {
        
    }

    /**
     * 发送礼包
     * @param Request $request
     * @return \think\Response
     * @throws \app\CustomException
     */
    public function  sendGiftPackByUid(Request $request)
    {
        $param = $request->param();
        if(!isset($param['uid'])) excep("uid为必传参数");
        if(!isset($param['coupon_id']))excep("优惠卷为必传参数");
        if(!isset($param['activity_source']) || strlen($param['activity_source'])==0)excep("活动来源为必传参数");
        if(!isset($param['activity_name'])|| strlen($param['activity_name'])==0)excep("活动名称为必传参数");
        $coupon_id = $param['coupon_id'];

        $activity_source = $param['activity_source'];
        $activity_name = $param['activity_name'];


        $redisresult = Cache::store("redis")->SISMEMBER("vinehoo.weide.uids",$param['uid']);
        if($redisresult)  excep("您已领取过该礼包");
        //核对用户是否存在
        $user = $this->checkuserinfo($param['uid'],$activity_source);
        if(!$user) excep("用户不存在，请核对");

        //推送队列发送礼包
        $mqresult = $this->sendGiftforweidubyuid($param['uid'],$coupon_id,$activity_name);
        if(!$mqresult) excep("推送礼包错误，请联系管理员");
        //存入redis集合
        $redisresult = Cache::store("redis")->sadd("vinehoo.weide.uids",$param['uid']);
        if(!$redisresult) excep("您已领取过该礼包。");
        return throwResponse();
    }

    /**
     * 核对用户存在
     * @param $uid
     * @return bool|void
     * @throws \app\CustomException
     */
    public function checkuserinfo($uid,$activity_source)
    {

        try {
            $url = env("ITEM.USER_URL").'/user/v3/profile/getUserInfo?field=uid,tripartite_source,created_time&uid='.$uid;//用户信息
            $data = httpCurl($url);
            $userInfo =  (array)json_decode($data,true);
            if($userInfo != false && $userInfo['error_code'] == 0){

                if(count($userInfo['data']['list'])>0){

                    $userdetail = $userInfo['data']['list'][0];

                    if($userdetail['tripartite_source'] != $activity_source )excep("你已经是老用户了");
                    if((time()-$userdetail['created_time'])<=(24*7*3600))return true;
                    excep("你已经是老用户了");
                }

//                if(count($userInfo['data']['list'])>0) return true;
            }
            return false;
        }catch (Exception $exception){
            Log::error($exception->getMessage());
            excep("操作错误：".$exception->getMessage());
        }

    }

    ///3
    public function sendGiftforweidubyuid($uid,$coupon_id,$activity_name ="")
    {
        try {
            $exchange_name = 'activity';
            $routing_key = 'activity.coupon.rabbits';
//            $msg = $coupon_id==2?"韦德优惠活动卷包":"联掌门户";
            $mqData = [
                "platform"=>"v3",
                "type"=>"coupon",
                "issue_num"=>1,
                "uid"=>$uid,
//                "coupon_id"=>[2],
                "coupon_id"=>[$coupon_id],
                "is_package"=>1,
                "callback"=>"",
                "telephone"=>"",
//                "activity_name"=>"韦德优惠活动卷包"
                "activity_name"=>$activity_name
            ];
//            echo json_encode($mqData);
            $resule = pushMqlist($exchange_name,$routing_key,$mqData);
            $curlbody = $resule->getBody()->getContents();
            $data = json_decode($curlbody,true);
            if($data['error_code'] == 0) return true;
            return false;
        }catch (\Exception $exception){
            Log::error($exception->getMessage());
            excep("操作错误：".$exception->getMessage());
        }

    }

    /**
     * 设置阅览量
     * @param Request $request
     * @return \think\Response
     */
    public function viewnums(Request $request)
    {

        Db::startTrans();
        try {
            $param = $request->param();
            foreach ($param as $v){
//                validate(\app\validate\ViewNums::class)->check($v);
                switch ($v['module']){
                    case 'ad':
                        foreach ($v['terms'] as $vv){
                            validate(\app\validate\ViewNums::class)->scene('numscheck')->check($vv);
                            \app\model\Ad::where('id',$vv['id'])->inc('viewnums',$vv['nums'])->update();
                        }
                        break;
                    case 'label':
                        foreach ($v['terms'] as $vv){
                            validate(\app\validate\ViewNums::class)->scene('numscheck')->check($vv);
                            \app\model\Label::where('id',$vv['id'])->inc('viewnums',$vv['nums'])->update();
                        }
                        break;
                    default:
                        Log::error("统计数量:异常传值:".json_encode($param));
                        break;
                }
            }

            Db::commit();
            return throwResponse((object)[]);
        }catch (\Exception $exception){
            Log::error("统计数量:".json_encode($param).$exception->getMessage());
            Db::rollback();
            return throwResponse((object)[],-1,$exception->getMessage());
        }

    }


    public function secondConfig(Request $request)
    {
        $rediskey = "vinehoo.second.config";
        $arr = Cache::get($rediskey, json_encode([
            'isopen'    => true,
            'topbg'     => 'https://images.vinehoo.com/vinehoo/activity/topbg2.png?t=nh',
            'centerbg'  => 'https://images.vinehoo.com/vinehoo/activity/centerbg2.png?t=nh',
            'bannerdt'  => 'https://images.vinehoo.com/vinehoo/activity/banner2.png?t=nh',
            'lpbk'      => 'https://images.vinehoo.com/vinehoo/activity/sp2.png?t=nh',
            'cpbk'      => 'https://images.vinehoo.com/vinehoo/activity/kapian2.png?t=nh',
            'cpds'      => 'https://images.vinehoo.com/vinehoo/activity/chanpin2.png?t=nh',
            'db'        => 'https://images.vinehoo.com/vinehoo/activity/dise2.png?t=nh',
            "tabbarImg" => "https://images.vinehoo.com/vinehoo/activity/tab_nianhuo.gif?t=nh",
            'bgcolor'   => '#B21605',
            'wzcolor'   => '#E80404',
            'wztext'    => '年货节'
        ]));
        return throwResponse(json_decode($arr, true));
    }

}
