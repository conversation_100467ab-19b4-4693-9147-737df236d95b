<?php

namespace app\controller\v3;

use think\Request;
use app\service\v3\DefaultKeyword as DefaultKeywordService;

class DefaultKeyword
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
    	$param = $request->param();

    	$response = DefaultKeywordService::list($param);
    	return throwResponse($response);
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\DefaultKeyword::class)->scene('defaultkeyword/form')->check($param);
        
        DefaultKeywordService::update($param);
        return throwResponse();
    }

    /**
     * 根据频道获取关键词
     * @param  \think\Request  $request
     * @return array
     */
    public function getKeyword(Request $request) {
    	$param = $request->param();
        validate(\app\validate\DefaultKeyword::class)->scene('defaultkeyword/getKeyword')->check($param);

    	$response = DefaultKeywordService::getKeyword($param);
    	return throwResponse($response);
    }
}
