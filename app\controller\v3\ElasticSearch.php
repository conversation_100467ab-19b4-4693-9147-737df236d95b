<?php

namespace app\controller\v3;

use app\BaseController;
use app\Request;
use Elasticsearch\ClientBuilder;

class ElasticSearch extends BaseController
{

    private $hosts = [];

    public function __construct()
    {
        $this->hosts[] = [
            'host' => env('ES.HOST','127.0.0.1'),
            'port' => env('ES.PORT',9200),
            'user' => env('ES.USER','root'),
            'pass' => env('ES.PASS','vinehoo666')
        ];
    }


    /**
     * 查询 es 商品详情
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPeriodInfo(Request $request): \think\Response
    {
        $param = $request->get();
        if (!isset($param['period']) || empty($param['period'])) {
            return throwResponse('参数错误');
        }
        // 获取筛选项字段
        $fields = ['id', 'title', 'brief', 'periods_type', 'product_id', 'banner_img', 'product_img'];
        $esWhere['bool']['must'][] = ['match' => ['id' => $param['period']]];
        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => $fields,
            'body' => [
                'query' => $esWhere,
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);

        $period_info = $data['list'][0] ?? [];
        return throwResponse($period_info);
    }

    /**
     * 根据期数 id 获取期数
     * @param $ids
     * @return mixed
     */
    public function getPeriodList($ids)
    {
        $esWhere['bool']['must'] = [];
        $ids = explode(',', $ids);
        $esWhere['bool']['must'][] = ['terms' => ['id' => $ids]];

        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            '_source' => [],
            'body' => [
                'query' => $esWhere,
                'size' => 100
            ]
        ];
        // 链接 es
        $es_client = new ClientBuilder();
        $client = $es_client->create()->setHosts($this->hosts)->build();
        // es 查询
        $es_data = $client->search($params);
        // es 解析后数据
        $data = get_es_result($es_data);
        return $data['list'];
    }

    /**
     * 根据期数获取订单
     * @param Request $request
     * @return \think\Response
     */
    public function getOrderByPeriod(Request $request): \think\Response
    {
        $param = $request->get();
        $es_ser = new \app\service\ElasticSearch();
        $result = $es_ser->getOrderByPeriod((int)$param['period']);
        return throwResponse($result);
    }

}