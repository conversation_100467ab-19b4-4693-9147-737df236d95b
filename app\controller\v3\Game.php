<?php

namespace app\controller\v3;

use think\facade\Db;
use think\Request;
use app\service\v3\Game as GameService;

class Game
{
    /**
     * 更新抽奖配置项
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRotaryDraw(Request $request) {
        $params = $request->param();
        if (!isset($params['id']) || empty($params['id'])) {
          return throwResponse([], 10001, '请选择修改项');
        }
        $game_ser = new GameService();
        // 操作信息
        $params['uid'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['operator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['operator_name']) {
            $params['operator_name'] = base64_decode($params['operator_name']);
        }
        if (isset($params['number']) && $params['number'] != '') {
            $params['number'] = str_replace('，', ',', $params['number']);
        }
        $params['updated_time'] = time();
        $result = $game_ser->updateRotaryDraw($params);
        if (!$result) {
            return throwResponse([],10002, '未更新数据');
        }
        return throwResponse($result);
    }

    /**
     * 更新黑名单抽奖配置
     * @param Request $request
     * @return \think\Response
     */
    public function updateRotaryDrawBlacklist(Request $request) {
        $params = $request->param();
        if (!isset($params['id']) || empty($params['id'])) {
            return throwResponse([], 10001, '请选择修改项');
        }
        $game_ser = new GameService();
        // 操作信息
        $params['uid'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['operator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['operator_name']) {
            $params['operator_name'] = base64_decode($params['operator_name']);
        }
        if (isset($params['number']) && $params['number'] != '') {
            $params['number'] = str_replace('，', ',', $params['number']);
        }
        $params['updated_time'] = time();
        $result = $game_ser->updateRotaryDrawBlacklist($params);
        if (!$result) {
            return throwResponse([],10002, '未更新数据');
        }
        return throwResponse($result);
    }

    /**
     * 更新兔头抽奖配置项
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRabbitRotaryDraw(Request $request) {
        $params = $request->param();
        if (!isset($params['id']) || empty($params['id'])) {
            return throwResponse([], 10001, '请选择修改项');
        }
        $game_ser = new GameService();
        // 操作信息
        $params['uid'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['operator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['operator_name']) {
            $params['operator_name'] = base64_decode($params['operator_name']);
        }
        if (isset($params['number']) && $params['number'] != '') {
            $params['number'] = str_replace('，', ',', $params['number']);
        }
        $params['updated_time'] = time();
        $result = $game_ser->updateRabbitRotaryDraw($params);
        if (!$result) {
            return throwResponse([],10002, '未更新数据');
        }
        return throwResponse($result);
    }

    /**
     * 获取抽奖配置项
     * @return mixed
     */
    public function getRotaryDraw() {

        $game_ser = new GameService();
        $list = $game_ser->getRotaryDraw();
        if (empty($list)) {
            return throwResponse([]);
        }
        return throwResponse(['list' => $list]);
    }

    /**
     * 获取黑名单抽奖配置项
     * @return mixed
     */
    public function getRotaryDrawBlacklist() {

        $game_ser = new GameService();
        $list = $game_ser->getRotaryDrawBlacklist();
        if (empty($list)) {
            return throwResponse([]);
        }
        return throwResponse(['list' => $list]);
    }

    /**
     * 添加订单购买记录
     * @param Request $request
     * @return \think\Response
     */
    public function salesOrderRecord(Request $request): \think\Response
    {
        $order = new GameService();
        $params = $request->post();
        $params['created_time'] = time();
        $result = $order->salesOrderRecord($params);
        if (!$result) {
            return throwResponse($result, 10002, '添加失败');
        }
        return throwResponse($request);
    }

    /**
     * 幸运抽奖
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function luckDraw(Request $request): \think\Response
    {
        $header = $request->header();
        $param = $request->param();

        $uid = $request->header('vinehoo-uid');

        $param['uid'] = $param['uid'] ?? 0;


        if (empty($uid) && empty($param['uid'])) {
//            $this->throwError('请先登录');
            return throwResponse(null, 10002, '请先登录');

        }

        if (!empty($uid)) {
            $param['uid'] = $uid;
        }

        if (empty($param['orderno'])) {
            $param['orderno'] = '';
//            $this->throwError('请传入订单');
        }

        $RabbitGameService  = new GameService();
        $is_res = $RabbitGameService->luckDraw($param,$header);

        if($is_res['status'] == true){
            return throwResponse($is_res['data']['data']);
//            return $this->success(['list'=>$is_res['data']]);
        }else{
//            return $this->failed($is_res['msg'],$is_res['flag']);
            return throwResponse(null, 50002, $is_res['msg']);

        }
    }

    /**
     * 兔头抽奖
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rabbitLuckDraw(Request $request): \think\Response
    {
        $header = $request->header();
        $param = $request->param();

        $uid = $request->header('vinehoo-uid');

        $param['uid'] = $param['uid'] ?? 0;
        if (empty($uid) && empty($param['uid'])) {
            return throwResponse(null, 10002, '请先登录');
        }

        if (!isset($param['is_rabbit'])) {
            $param['is_rabbit'] = 0;
        }

        // 查询用户是否存在免费抽奖次数
        $start = date('Y-m-d 23:59:59', strtotime("-1 day", time()));
        $end_time = date('Y-m-d 23:59:39', time());
        $lottery_count = Db::name('rabbit_share_draw')
            ->where([['addtime', '>', $start],['addtime', '<', $end_time]])
            ->where('uid', $uid)
            ->count();
        if ($lottery_count > 0 && $param['is_rabbit'] != 1) {
            return throwResponse(null, 50002, '今日免费抽奖次数已用尽');
        }

        // 查询用户
        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $user_url = $user_url . '?uid=' . $uid . '&field=uid,user_level,nickname,telephone,rabbit';
        $user_list = get_url($user_url);
        $user_list = get_interior_http_response($user_list);
        $user_list = $user_list['list'] ?? [];
        $param['uname'] = $user_list[0]['telephone'] ?? 0;
        $param['uid'] = $param['uid'] ? : $uid;

        // 用户兔头
        $user_rabbit = $user_list[0]['rabbit'] ?? 0;
        // 使用兔头抽奖
        if ($param['is_rabbit'] == 1) {
            if ($user_rabbit < 10) {
                return throwResponse(null, 10002, '可用兔头不足');
            }
        }
        // 开始抽奖
        $RabbitGameService  = new GameService();
        $is_res = $RabbitGameService->rabbitLuckDraw($param,$header);

        if($is_res['status'] == true){
            return throwResponse($is_res['data']['data']);
//            return $this->success(['list'=>$is_res['data']]);
        }else{
//            return $this->failed($is_res['msg'],$is_res['flag']);
            return throwResponse(null, 10002, $is_res['msg']);

        }

    }

    /**
     * 中奖记录
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function rabbitShareDrawList(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid');
        if (empty($uid)) {
            return throwResponse(null, 10002, '请先登录');
        }
        $limit = $request->get('limit', 10);
        $RabbitGameService  = new GameService();
        $result = $RabbitGameService->rabbitShareDrawList($uid, $limit);
        return throwResponse($result);
    }

    /**
     * 查询用户抽奖信息
     * @param Request $request
     * @return \think\Response
     */
    public function getUserLotteryInfo(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid');
        if (!$uid) {
            return throwResponse(null, 10002, '请先登录');
        }
        $RabbitGameService  = new GameService();
        $result = $RabbitGameService->getUserLotteryInfo($uid);
        return throwResponse($result);
    }

    /**
     * 查询新人用户抽奖信息
     * @param Request $request
     * @return \think\Response
     */
    public function getNewcomerLotteryInfo(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid');
        if (!$uid) {
            return throwResponse(null, 10002, '请先登录');
        }
        $RabbitGameService  = new GameService();
        $result = $RabbitGameService->getNewcomerLotteryInfo($uid);
        return throwResponse($result);
    }

    /**
     * 获取抽奖配置项
     * @return mixed
     */
    public function getRabbitRotaryDraw() {
        $game_ser = new GameService();
        $list = $game_ser->getRabbitRotaryDraw();
        if (empty($list)) {
            return throwResponse([]);
        }
        return throwResponse(['list' => $list]);
    }

    /**
     * 查询订单状态
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUserOrderStatus(Request $request): \think\Response
    {
        $orderno = $request->get('orderno', '');
        if (empty($orderno)) {
            return throwResponse(null, 10001, '请选择订单');
        }
        $game_ser = new GameService();
        $re = $game_ser->getUserOrderStatus($orderno);
        if (!empty($re)) {
            if ($re['status'] == 0) {
                return throwResponse(true);
            } else {
                return throwResponse(false);
            }
        }
        return throwResponse(null, 10001, '未查询到订单');
    }

    /**
     * 查询用户剩余可抽次数
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function getUserOrderCount(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid');
        if (empty($uid)) {
            return throwResponse(null, 10002, '请先登录');
        }
        $game_ser = new GameService();
        $re = $game_ser->getUserOrderCount((int)$uid);
        return throwResponse($re);
    }

    /**
     * 新人兔头抽奖
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function newRabbitLuckDraw(Request $request): \think\Response
    {
        $header = $request->header();
        $param = $request->param();

        $uid = $request->header('vinehoo-uid');

        $param['uid'] = $param['uid'] ?? 0;
        if (empty($uid) && empty($param['uid'])) {
            return throwResponse(null, 10002, '请先登录');
        }

        if (!isset($param['is_rabbit'])) {
            $param['is_rabbit'] = 0;
        }
        $RabbitGameService  = new GameService();
        // 查询用户是否存在免费抽奖次数, 每日免费单抽
        $lottery_count = $RabbitGameService->getNewcomerUseDraw($uid);
        // 查询用户分享次数
        $share_count = $RabbitGameService->getNewcomerShare($uid);
        if ($lottery_count > 0 && $param['is_rabbit'] != 1 && $share_count < 1) {
            return throwResponse(null, 50002, '今日免费抽奖次数已用尽');
        }
        // 使用分享次数抽奖
        if ($lottery_count > 0 && $share_count > 0 && $param['is_rabbit'] != 1) {
            $res = $RabbitGameService->decNewcomerShare($uid);
            if ($res !== true){
                return throwResponse(null, 50002, $res);
            }
        }
        // 查询用户
        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $user_url = $user_url . '?uid=' . $uid . '&field=uid,user_level,nickname,telephone,rabbit,is_new_user';
        $user_list = get_url($user_url);
        $user_list = get_interior_http_response($user_list);
        if (empty($user_list['list'][0])) {
            return throwResponse(null, 10002, '用户信息获取失败');
        }
        $user = $user_list['list'][0];
        $param['uname'] = $user['telephone'] ?? 0;
        $param['uid'] = $param['uid'] ? : $uid;
        // 用户兔头
        $param['is_new_user'] = $user['is_new_user'] ?? 0;

        // 用户兔头
        $user_rabbit = $user['rabbit'] ?? 0;
        // 使用兔头抽奖
        if ($param['is_rabbit'] == 1) {
            if ($user_rabbit < 10) {
                return throwResponse(null, 10002, '可用兔头不足');
            }
        }
        // 开始抽奖
        $is_res = $RabbitGameService->newRabbitLuckDraw($param,$header);

        if($is_res['status'] == true){
            return throwResponse($is_res['data']['data']);
//            return $this->success(['list'=>$is_res['data']]);
        }else{
//            return $this->failed($is_res['msg'],$is_res['flag']);
            return throwResponse(null, 10002, $is_res['msg']);

        }

    }

    /**
     * 获取新人抽奖配置项
     * @return mixed
     */
    public function getNewRabbitRotaryDraw() {
        $game_ser = new GameService();
        $list = $game_ser->getNewRabbitRotaryDraw();
        if (empty($list)) {
            return throwResponse([]);
        }
        return throwResponse(['list' => $list]);
    }

    /**
     * 新人抽奖中奖记录
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function newRabbitShareDrawList(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid');
        if (empty($uid)) {
            return throwResponse(null, 10002, '请先登录');
        }
        $limit = $request->get('limit', 10);
        $RabbitGameService  = new GameService();
        $result = $RabbitGameService->newRabbitShareDrawList($uid, $limit);
        return throwResponse($result);
    }

    /**
     * 分享增加抽奖次数
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DbException
     */
    public function newcomerShare(Request $request): \think\Response
    {
        $uid = $request->header('vinehoo-uid');
        if (empty($uid)) {
            return throwResponse(null, 10002, '请先登录');
        }
        $RabbitGameService  = new GameService();
        $request = $RabbitGameService->newcomerShare($uid);
        return throwResponse($request);
    }

    /**
     * 更新新人抽奖配置项
     * @param Request $request
     * @return \think\Response
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateNewRotaryDraw(Request $request)
    {
        $params = $request->param();
        if (!isset($params['id']) || empty($params['id'])) {
            return throwResponse([], 10001, '请选择修改项');
        }
        $game_ser = new GameService();
        // 操作信息
        $params['uid'] = $request->header('vinehoo-uid');
        // 获取当前操作人
        $params['operator_name'] = $request->header('vinehoo-vos-name', '');
        if ($params['operator_name']) {
            $params['operator_name'] = base64_decode($params['operator_name']);
        }
        if (isset($params['number']) && $params['number'] != '') {
            $params['number'] = str_replace('，', ',', $params['number']);
        }
        $params['updated_time'] = time();
        $result = $game_ser->updateNewRabbitRotaryDraw($params);
        if (!$result) {
            return throwResponse([], 10002, '未更新数据');
        }
        return throwResponse(1);
    }

}