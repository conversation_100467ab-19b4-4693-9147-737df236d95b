<?php

namespace app\controller\v3;

use think\Request;
use app\service\v3\HotKeyword as HotKeywordService;

class HotKeyword
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        
        $response = HotKeywordService::list($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\HotKeyword::class)->scene('hotkeyword/form')->check($param);
        
        HotKeywordService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\HotKeyword::class)->scene('hotkeyword/form')->check($param);
        
        HotKeywordService::update($param);
        return throwResponse();
    }

    /**
     * 更改状态
     * @param  \think\Request  $request
     * @return array
     */
    public function status(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\HotKeyword::class)->scene('hotkeyword/is_show')->check($param);
        
        HotKeywordService::update($param);
        return throwResponse();
    }

    /**
     * 删除
     * @param  \think\Request  $request
     * @return array
     */
    public function delete(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        if(!$request->header('vinehoo-vos-name')){
            excep("用户名称错误");
        }
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\HotKeyword::class)->scene('hotkeyword/login')->check($param);

        HotKeywordService::delete($param);
        return throwResponse();
    }

    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function clientlist(Request $request) {
        $param = $request->param();

        $response = HotKeywordService::clientlist($param);
        return throwResponse($response);
    }

}
