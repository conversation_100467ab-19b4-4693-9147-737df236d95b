<?php

namespace app\controller\v3;

use app\BaseController;
use app\ErrorCode;
use think\cache\driver\Redis;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Validate;
use think\Request;
use think\Response;
use app\service\v3\HoverButton as HoverButtonService;

class HoverButton extends BaseController
{
    protected $options = [
        'host'       => '127.0.0.1',
        'port'       => 6379,
        'password'   => '',
        'select'     => 0,
        'timeout'    => 0,
        'expire'     => 0,
        'persistent' => false,
        'prefix'     => '',
        'tag_prefix' => 'tag:',
        'serialize'  => [],
    ];

    protected function initialize()
    {
        parent::initialize(); // TODO: Change the autogenerated stub
        $this->options["host"]= env("CACHE.HOST","redis");
        $this->options["port"]= env("CACHE.PORT","3306");
        $this->options["password"]= env("CACHE.PASSWORD","");
        $this->options["prefix"]= env("CACHE.prefix","vinehoo");
    }

    /**
     * 列表
     * @return \think\response\Json
     */
    public function list(Request $request)
    {

        $params = $request->param();
        $result = HoverButtonService::list($params);
        return throwResponse($result);

    }

    public function index(Request $request)
    {

        //参数验证
        $validate = Validate::rule([
            'from|简码' => 'require|max:30',
        ]);
        if (!$validate->check($request->param())) excep($validate->getError());


//        $cache = new Redis($this->options);
        $data = Cache::get('REDIS_KEY_HOVER_BUTTON_INDEX');

        if($data){
            $code = (array)json_decode($data,true);//数据

            if ($code['data']['show'] == 1) {
                if (isset($code['data']['start_time']) && isset($code['data']['start_time'])) {
                    $now_time = time();
                    if (!($now_time >= strtotime($code['data']['start_time']) && $now_time <= strtotime($code['data']['end_time']))) {
                        $code['data']['show'] = 0;
                    }
                }
            }

            $extstr = strstr($code['data']['params']['icon'],"?",true)?:$code['data']['params']['icon'];
            $w =180; $h=180;
            $ext = strrev(strchr(strrev($extstr),'.',true));//后缀
            //适配url
//            strpos($code['data']['params']['icon'],"?")? $code['data']['params']['icon'] .= '&x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext: $code['data']['params']['icon']=$code['data']['params']['icon'];
            if(strpos($code['data']['params']['icon'],"?")) $code['data']['params']['icon'] .= '&x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
            strpos($code['data']['params']['url'],"?")?$code['data']['params']['url'] .= "&app=".$request->param("from"):$code['data']['params']['url'] .= "?app=".$request->param("from");

            //满足条件进入流程，详情查看HoverButton.plantuml
            if($code['data']['external_action']== '1'&&$code['data']['show']==1){//check_newuser
                $param = ['uid'=>$request->header('vinehoo-uid')];

                $newUser = \app\service\v3\Common::chackNewUser($param);

                switch ($newUser){
                    case 1:
//                    $code['data']['show'] = 1;
                        return json_encode($code);
                        break;
                    case 3:
                        return json_encode($code);
                        break;
                    case 2:
                        $code['data']['show'] = 0;
                        return json_encode($code);
                        break;
                    default:
                        $code['data']['show'] = 0;
                        return json_encode($code);
                }
            }else{
                return Response::create($code, 'json', 200)->header([]);
//                return json_encode($code);
            }



        }else{
            return throwResponse([],-1,"获取数据失败，请联系管理员");
        }

    }

    /**
     * 更新
     * @param Request $request
     * @return Response|void
     */
    public function update(Request $request)
    {

        validate(\app\validate\HoverButton::class)->scene('edit')->check($request->param());
        $params = $request->param();
        //验证状态
        HoverButtonService::validate($params)?excep("悬浮按钮同时仅能开启一个"):"";
        $result = HoverButtonService::update($params);
        return throwResponse($result);
        $param = $request->param();

    }

    //创建
    public function create(Request $request)
    {
        $params = $request->param();
        validate(\app\validate\HoverButton::class)->scene('create')->check($request->param());
        //验证状态
        HoverButtonService::validate($params)?excep("悬浮按钮同时仅能开启一个"):"";
        HoverButtonService::create($params);
        return throwResponse();
    }

    //删除
    public function delete(Request $request)
    {
        $params = $request->param();
        //参数验证
        $validate = Validate::rule([
            'id' => 'require|max:30',
        ]);
        if (!$validate->check($request->param()))  excep($validate->getError());

        $result = HoverButtonService::delete($params);
        return throwResponse($result);
    }

    //修改显示状态
    public function showsave(Request $request)
    {
        $params = $request->param();
        validate(\app\validate\HoverButton::class)->scene('showsave')->check($request->param());
        //验证状态
        HoverButtonService::validate($params)?excep("悬浮按钮同时仅能开启一个"):"";
        $result = HoverButtonService::showsave($params);
        return throwResponse($result);
    }

}