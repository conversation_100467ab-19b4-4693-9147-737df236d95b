<?php

namespace app\controller\v3;

use think\Request;
use app\service\v3\Label as LabelService;



class Label
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        
        $response = LabelService::list($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['created_at'] = time();
        validate(\app\validate\Label::class)->scene('label/form')->check($param);

        LabelService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Label::class)->scene('label/form')->check($param);
        
        LabelService::update($param);
        return throwResponse();
    }

    /**
     * 更改状态
     * @param  \think\Request  $request
     * @return array
     */
    public function status(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator_name'] = base64_decode($request->header('vinehoo-vos-name'));

        validate(\app\validate\Label::class)->scene('label/status')->check($param);

        \app\model\Label::where("id",$param["id"])->save($param);
//        LabelService::update($param);
        return throwResponse();
    }


    /**
     * 客户端 列表
     * @param Request $request
     * @return \think\Response
     */
    public function clientlist(Request $request)
    {
        $param = $request->param();
        $param['vinehoo_client_version'] = $request->header('vinehoo-client-version');
        $param['vinehoo_client'] = $request->header('vinehoo-client');
        if(isset($param['client']) && ($param['client'] == 5)) $param['client'] = 1;

        $response = LabelService::clientlist($param);
        return throwResponse($response);
    }

    public function detail(Request $request)
    {
        $param = $request->param();
        if(!isset($param['id']) || strlen($param['id'])<=0){
            excep("数据不存在，请检查id");
        }
        $response = \app\service\v3\Label::detail($param);
        return throwResponse($response);
    }

}
