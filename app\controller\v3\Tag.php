<?php

namespace app\controller\v3;

use think\Request;
use app\service\v3\Tag as TagService;



class Tag
{
    /**
     * 列表
     * @param  \think\Request  $request
     * @return array
     */
    public function list(Request $request) {
        $param = $request->param();
        
        $response = TagService::list($param);
        return throwResponse($response);
    }

    /**
     * 前端列表
     * @param  \think\Request  $request
     * @return array
     */
    public function clientlist(Request $request) {
        $param = $request->param();
        $param['page'] = 1;
        $param['limit'] = 1000;
        $param['status'] = 1;
        $response = TagService::list($param);
        return throwResponse($response);
    }

    /**
     * 添加
     * @param  \think\Request  $request
     * @return array
     */
    public function create(Request $request) {
        $time = time();
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['created_at'] = $time;
        $param['update_at'] = $time;
        validate(\app\validate\Tag::class)->scene('tag/form')->check($param);

        TagService::create($param);
        return throwResponse();
    }

    /**
     * 编辑
     * @param  \think\Request  $request
     * @return array
     */
    public function update(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['update_at'] = time();
        validate(\app\validate\Tag::class)->scene('tag/update')->check($param);
        
        TagService::update($param);
        return throwResponse();
    }

    /**
     * 更改状态
     * @param  \think\Request  $request
     * @return array
     */
    public function status(Request $request) {
        $param = $request->param();
        $param['operator_id'] = $request->header('vinehoo-uid');
        $param['operator'] = base64_decode($request->header('vinehoo-vos-name'));
        $param['update_at'] = time();
        validate(\app\validate\Tag::class)->scene('tag/status')->check($param);

        \app\model\Tag::where("id",$param["id"])->save($param);
        return throwResponse();
    }


}
