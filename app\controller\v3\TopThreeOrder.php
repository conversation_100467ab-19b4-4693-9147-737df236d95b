<?php


namespace app\controller\v3;


use app\BaseController;
use think\facade\Validate;
use think\Request;
use app\service\v3\TopThreeOrder as TopThreeOrderService;

class TopThreeOrder extends BaseController
{
    /**
     * Description:首三单列表
     * Author: zrc
     * Date: 2023/6/25
     * Time: 11:44
     * @param Request $request
     * @return \think\Response
     */
    public function list(Request $request)
    {
        $params = $request->param();
        //数据验证
        $validate = Validate::rule([
            'page|页码'  => 'require|number|>:0',
            'limit|条数' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            excep($validate->getError());
        }
        $TopThreeOrderService = new TopThreeOrderService();
        $result               = $TopThreeOrderService->list($params);
        return throwResponse($result);
    }

    /**
     * Description:首三单修改
     * Author: zrc
     * Date: 2023/6/25
     * Time: 11:34
     * @param Request $request
     * @return \think\Response
     * @throws \app\CustomException
     */
    public function update(Request $request)
    {
        $params             = $request->param();
        $params['operator'] = $request->header('vinehoo-uid');
        //数据验证
        $validate = Validate::rule([
            'id|自增ID'      => 'require|number|>:0',
            'operator|操作人' => 'require|number|>:0',
        ]);
        if (!$validate->check($params)) {
            excep($validate->getError());
        }
        $TopThreeOrderService = new TopThreeOrderService();
        $result               = $TopThreeOrderService->update($params);
        return throwResponse($result);
    }
}