{"client_0_channel_0": {"list": [{"id": 1, "channel": 0, "label_name": "酒闻", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193627459YdDcKQZjk_FfE8KAnwf.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 31, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 31, "path_name": "酒闻资讯", "code": "WinesmellList", "client": "0,1", "ios_path": "SmellWineListViewController", "android_path": "com.stg.rouge.activity.WinesmellListActivity", "mini_path": "/packageD/pages/wine-smell-news/wine-smell-news", "h5_path": "/packageD/pages/wine-smell-news/wine-smell-news", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-24 09:51:39", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 18, "channel": 0, "label_name": "精品酒会", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193638984BAGGnh84M_zG6Saeh8n.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 26, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 26, "path_name": "精品酒会", "code": "ReceptionList", "client": "0,1", "ios_path": "FineReceptionListViewController", "android_path": "com.stg.rouge.activity.ReceptionListActivity", "mini_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "h5_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-12 18:19:37", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 17, "channel": 0, "label_name": "兔头商店", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193649152bx5znWSHy_hbz6sPHpZ.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 16, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 16, "path_name": "兔头商店", "code": "RabbitShop", "client": "0,1,2,3", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebFullActivity", "mini_path": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "h5_path": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:41:14", "operator_name": "张红勇"}, "ad_path_param": [{"id": 214, "lid": 17, "pid": 16, "peid": 22, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}, {"id": 215, "lid": 17, "pid": 16, "peid": 23, "title": "页面参数1", "ios_key": "url", "ios_val": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "android_key": "url", "android_val": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 216, "lid": 17, "pid": 16, "peid": 24, "title": "页面参数2", "ios_key": "isHideNav", "ios_val": "YES", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 217, "lid": 17, "pid": 16, "peid": 73, "title": "页面参数3", "ios_key": "isStatusBar", "ios_val": "YES", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 16, "channel": 0, "label_name": "每日任务", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193660157R7BTiGATG_xQXmameAf.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 11, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 11, "path_name": "今日任务", "code": "EveryTask", "client": "0,1", "ios_path": "DailyCheckViewController", "android_path": "com.stg.rouge.activity.EveryTaskCenterActivity", "mini_path": "/packageE/pages/daily-tasks/daily-tasks", "h5_path": "/packageE/pages/daily-tasks/daily-tasks", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:32:29", "operator_name": "张红勇"}, "ad_path_param": [{"id": 218, "lid": 16, "pid": 11, "peid": 15, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 13, "channel": 0, "label_name": "社区", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193710054Dr2kdrChA_sxAHX2zBC.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 45, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 45, "path_name": "首页发现", "code": "MainFind", "client": "0,1", "ios_path": "go<PERSON>ain", "android_path": "go<PERSON>ain", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-06-13 14:18:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 225, "lid": 13, "pid": 45, "peid": 70, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "2", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}], "total": 5}, "client_0_channel_1": {"list": [], "total": 0}, "client_0_channel_2": {"list": [{"id": 22, "channel": 2, "label_name": "004", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444447890BczDG54sN_0033ImPzly1h2al7gp0o2j618p0u040e02.jpg?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,jpg", "param": null, "badge": "", "path_id": 11, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 11, "path_name": "今日任务", "code": "EveryTask", "client": "0,1", "ios_path": "DailyCheckViewController", "android_path": "com.stg.rouge.activity.EveryTaskCenterActivity", "mini_path": "/packageE/pages/daily-tasks/daily-tasks", "h5_path": "/packageE/pages/daily-tasks/daily-tasks", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:32:29", "operator_name": "张红勇"}, "ad_path_param": [{"id": 26, "lid": 22, "pid": 11, "peid": 15, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 21, "channel": 2, "label_name": "003", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444425542PprMrY4Zx_5500A062-666A-401f-9536-7324BF121E91.png?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444430050ZTXsDQ2mR_5500A062-666A-401f-9536-7324BF121E91.png", "path_id": 22, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 22, "path_name": "优惠券", "code": "MyCoupon", "client": "0,1,2,3", "ios_path": "CouponsViewController", "android_path": "com.stg.rouge.activity.CouponListActivity", "mini_path": "/coupon-list/coupon-list", "h5_path": "/coupon-list/coupon-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:48:45", "operator_name": "张红勇"}, "ad_path_param": [{"id": 25, "lid": 21, "pid": 22, "peid": 30, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 20, "channel": 2, "label_name": "002", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444405923FGp6tnTMr_8f986ba692f73d8c07f21ab005ddcf3d.png?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444414052rsjdT8KpF_8f986ba692f73d8c07f21ab005ddcf3d.png", "path_id": 19, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 19, "path_name": "购物车", "code": "shopCar", "client": "0,1,2,3", "ios_path": "ShoppingCartViewController", "android_path": "com.stg.rouge.activity.ShopCarActivity", "mini_path": "/packageB/pages/shopping-cart/shopping-cart", "h5_path": "/packageB/pages/shopping-cart/shopping-cart", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:45:16", "operator_name": "张红勇"}, "ad_path_param": [{"id": 24, "lid": 20, "pid": 19, "peid": 27, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 19, "channel": 2, "label_name": "001", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/16534443911258WYtimirK_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/165344439448032JxhM5Be_编组 <EMAIL>", "path_id": 14, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 14, "path_name": "我的酒会", "code": "MyWineParty", "client": "0,1,2,3", "ios_path": "MyRecepitonViewController", "android_path": "com.stg.rouge.activity.MyReceptionActivity", "mini_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "h5_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:51", "operator_name": "张红勇"}, "ad_path_param": [{"id": 23, "lid": 19, "pid": 14, "peid": 20, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}], "total": 4}, "client_0_channel_3": {"list": [], "total": 0}, "client_0_channel_4": {"list": [], "total": 0}, "client_0_channel_5": {"list": [{"id": 11, "channel": 5, "label_name": "我的视频", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656467810605GJmrXYMmJ_我的视频.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 12, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 12, "path_name": "我的视频", "code": "MyVideo", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:34:20", "operator_name": "张红勇"}, "ad_path_param": [{"id": 71, "lid": 11, "pid": 12, "peid": 16, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 72, "lid": 11, "pid": 12, "peid": 17, "title": "页面参数", "ios_key": "type", "ios_val": "1", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 2, "channel": 5, "label_name": "我的帖子", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254625003d3pb8w3tH_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/20220703/1656811729657xJBDf45cY_1656383225521H2sSA4kjy_hot.gif?w=46&h=46", "path_id": 13, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 13, "path_name": "我的帖子", "code": "MyPost", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:03", "operator_name": "张红勇"}, "ad_path_param": [{"id": 73, "lid": 2, "pid": 13, "peid": 18, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 74, "lid": 2, "pid": 13, "peid": 19, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 3, "channel": 5, "label_name": "我的酒会", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254823860bdfAf2sR8_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 14, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 14, "path_name": "我的酒会", "code": "MyWineParty", "client": "0,1,2,3", "ios_path": "MyRecepitonViewController", "android_path": "com.stg.rouge.activity.MyReceptionActivity", "mini_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "h5_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:51", "operator_name": "张红勇"}, "ad_path_param": [{"id": 109, "lid": 3, "pid": 14, "peid": 20, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 4, "channel": 5, "label_name": "我的收藏", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254874154NhX2kHPEk_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 15, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 15, "path_name": "我的收藏", "code": "MyCollect", "client": "0,1,2,3", "ios_path": "MyCollectViewController", "android_path": "com.stg.rouge.activity.PersonCollectActivity", "mini_path": "/packageE/pages/my-collection/my-collection", "h5_path": "/packageE/pages/my-collection/my-collection", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:36:57", "operator_name": "张红勇"}, "ad_path_param": [{"id": 114, "lid": 4, "pid": 15, "peid": 21, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 5, "channel": 5, "label_name": "购物车", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255137820iedjK3nfP_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 19, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 19, "path_name": "购物车", "code": "shopCar", "client": "0,1,2,3", "ios_path": "ShoppingCartViewController", "android_path": "com.stg.rouge.activity.ShopCarActivity", "mini_path": "/packageB/pages/shopping-cart/shopping-cart", "h5_path": "/packageB/pages/shopping-cart/shopping-cart", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:45:16", "operator_name": "张红勇"}, "ad_path_param": [{"id": 59, "lid": 5, "pid": 19, "peid": 27, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 6, "channel": 5, "label_name": "体验券", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/16522554126283j6Q6cezH_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 20, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 20, "path_name": "体验券", "code": "ExperienceVoucher", "client": "0,1,2", "ios_path": "MyExperienceVoucherViewController", "android_path": "com.stg.rouge.activity.ExperienceVoucherActivity", "mini_path": "/packageE/pages/experience-coupon-list/experience-coupon-list", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:47:38", "operator_name": "张红勇"}, "ad_path_param": [{"id": 116, "lid": 6, "pid": 20, "peid": 80, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "<PERSON><PERSON><PERSON><PERSON>", "h5_val": "1"}]}, {"id": 7, "channel": 5, "label_name": "在线客服", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255845705S4Dwp8QET_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 25, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 25, "path_name": "在线客服", "code": "kefu", "client": "0,1", "ios_path": "ordinarykefu", "android_path": "zaixian.kefu", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:56:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 135, "lid": 7, "pid": 25, "peid": 34, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 23, "channel": 5, "label_name": "申请认证", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656468807755TQf3tZa5T_申请认证.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 17, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 17, "path_name": "申请认证", "code": "ApplyCertify", "client": "0,1,2,3", "ios_path": "applyCertificationViewController", "android_path": "com.stg.rouge.activity.CertifyActivity", "mini_path": "/packageE/pages/certification-apply/certification-apply", "h5_path": "/packageE/pages/certification-apply/certification-apply", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:41:48", "operator_name": "张红勇"}, "ad_path_param": [{"id": 118, "lid": 23, "pid": 17, "peid": 25, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 9, "channel": 5, "label_name": "收货地址", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255983366GwkfWA8jG_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 18, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 18, "path_name": "收货地址", "code": "AddressManage", "client": "0,1,2,3", "ios_path": "AddressManagementViewController", "android_path": "com.stg.rouge.activity.AddressManageActivity", "mini_path": "/packageE/pages/address-management/address-management", "h5_path": "/packageE/pages/address-management/address-management", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:42:40", "operator_name": "张红勇"}, "ad_path_param": [{"id": 125, "lid": 9, "pid": 18, "peid": 26, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}], "total": 9}, "client_1_channel_0": {"list": [{"id": 1, "channel": 0, "label_name": "酒闻", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193627459YdDcKQZjk_FfE8KAnwf.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 31, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 31, "path_name": "酒闻资讯", "code": "WinesmellList", "client": "0,1", "ios_path": "SmellWineListViewController", "android_path": "com.stg.rouge.activity.WinesmellListActivity", "mini_path": "/packageD/pages/wine-smell-news/wine-smell-news", "h5_path": "/packageD/pages/wine-smell-news/wine-smell-news", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-24 09:51:39", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 18, "channel": 0, "label_name": "精品酒会", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193638984BAGGnh84M_zG6Saeh8n.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 26, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 26, "path_name": "精品酒会", "code": "ReceptionList", "client": "0,1", "ios_path": "FineReceptionListViewController", "android_path": "com.stg.rouge.activity.ReceptionListActivity", "mini_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "h5_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-12 18:19:37", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 17, "channel": 0, "label_name": "兔头商店", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193649152bx5znWSHy_hbz6sPHpZ.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 16, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 16, "path_name": "兔头商店", "code": "RabbitShop", "client": "0,1,2,3", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebFullActivity", "mini_path": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "h5_path": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:41:14", "operator_name": "张红勇"}, "ad_path_param": [{"id": 214, "lid": 17, "pid": 16, "peid": 22, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}, {"id": 215, "lid": 17, "pid": 16, "peid": 23, "title": "页面参数1", "ios_key": "url", "ios_val": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "android_key": "url", "android_val": "/packageB/pages/rabbit-head-shop/rabbit-head-shop", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 216, "lid": 17, "pid": 16, "peid": 24, "title": "页面参数2", "ios_key": "isHideNav", "ios_val": "YES", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 217, "lid": 17, "pid": 16, "peid": 73, "title": "页面参数3", "ios_key": "isStatusBar", "ios_val": "YES", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 16, "channel": 0, "label_name": "每日任务", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193660157R7BTiGATG_xQXmameAf.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 11, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 11, "path_name": "今日任务", "code": "EveryTask", "client": "0,1", "ios_path": "DailyCheckViewController", "android_path": "com.stg.rouge.activity.EveryTaskCenterActivity", "mini_path": "/packageE/pages/daily-tasks/daily-tasks", "h5_path": "/packageE/pages/daily-tasks/daily-tasks", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:32:29", "operator_name": "张红勇"}, "ad_path_param": [{"id": 218, "lid": 16, "pid": 11, "peid": 15, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 13, "channel": 0, "label_name": "社区", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/203208/1665193710054Dr2kdrChA_sxAHX2zBC.png?w=120&h=120&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 45, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 45, "path_name": "首页发现", "code": "MainFind", "client": "0,1", "ios_path": "go<PERSON>ain", "android_path": "go<PERSON>ain", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-06-13 14:18:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 225, "lid": 13, "pid": 45, "peid": 70, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "2", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}], "total": 5}, "client_1_channel_1": {"list": [], "total": 0}, "client_1_channel_2": {"list": [{"id": 22, "channel": 2, "label_name": "004", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444447890BczDG54sN_0033ImPzly1h2al7gp0o2j618p0u040e02.jpg?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,jpg", "param": null, "badge": "", "path_id": 11, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 11, "path_name": "今日任务", "code": "EveryTask", "client": "0,1", "ios_path": "DailyCheckViewController", "android_path": "com.stg.rouge.activity.EveryTaskCenterActivity", "mini_path": "/packageE/pages/daily-tasks/daily-tasks", "h5_path": "/packageE/pages/daily-tasks/daily-tasks", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:32:29", "operator_name": "张红勇"}, "ad_path_param": [{"id": 26, "lid": 22, "pid": 11, "peid": 15, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 21, "channel": 2, "label_name": "003", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444425542PprMrY4Zx_5500A062-666A-401f-9536-7324BF121E91.png?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444430050ZTXsDQ2mR_5500A062-666A-401f-9536-7324BF121E91.png", "path_id": 22, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 22, "path_name": "优惠券", "code": "MyCoupon", "client": "0,1,2,3", "ios_path": "CouponsViewController", "android_path": "com.stg.rouge.activity.CouponListActivity", "mini_path": "/coupon-list/coupon-list", "h5_path": "/coupon-list/coupon-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:48:45", "operator_name": "张红勇"}, "ad_path_param": [{"id": 25, "lid": 21, "pid": 22, "peid": 30, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 20, "channel": 2, "label_name": "002", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444405923FGp6tnTMr_8f986ba692f73d8c07f21ab005ddcf3d.png?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/1653444414052rsjdT8KpF_8f986ba692f73d8c07f21ab005ddcf3d.png", "path_id": 19, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 19, "path_name": "购物车", "code": "shopCar", "client": "0,1,2,3", "ios_path": "ShoppingCartViewController", "android_path": "com.stg.rouge.activity.ShopCarActivity", "mini_path": "/packageB/pages/shopping-cart/shopping-cart", "h5_path": "/packageB/pages/shopping-cart/shopping-cart", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:45:16", "operator_name": "张红勇"}, "ad_path_param": [{"id": 24, "lid": 20, "pid": 19, "peid": 27, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 19, "channel": 2, "label_name": "001", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/16534443911258WYtimirK_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/165344439448032JxhM5Be_编组 <EMAIL>", "path_id": 14, "created_at": "1970-01-01 08:00:00", "channel_name": "秒发", "client_path": {"id": 14, "path_name": "我的酒会", "code": "MyWineParty", "client": "0,1,2,3", "ios_path": "MyRecepitonViewController", "android_path": "com.stg.rouge.activity.MyReceptionActivity", "mini_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "h5_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:51", "operator_name": "张红勇"}, "ad_path_param": [{"id": 23, "lid": 19, "pid": 14, "peid": 20, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}], "total": 4}, "client_1_channel_3": {"list": [], "total": 0}, "client_1_channel_4": {"list": [], "total": 0}, "client_1_channel_5": {"list": [{"id": 11, "channel": 5, "label_name": "我的视频", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656467810605GJmrXYMmJ_我的视频.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 12, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 12, "path_name": "我的视频", "code": "MyVideo", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:34:20", "operator_name": "张红勇"}, "ad_path_param": [{"id": 71, "lid": 11, "pid": 12, "peid": 16, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 72, "lid": 11, "pid": 12, "peid": 17, "title": "页面参数", "ios_key": "type", "ios_val": "1", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 2, "channel": 5, "label_name": "我的帖子", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254625003d3pb8w3tH_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "https://images.vinehoo.com/vinehoo/vos/marketing/20220703/1656811729657xJBDf45cY_1656383225521H2sSA4kjy_hot.gif?w=46&h=46", "path_id": 13, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 13, "path_name": "我的帖子", "code": "MyPost", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:03", "operator_name": "张红勇"}, "ad_path_param": [{"id": 73, "lid": 2, "pid": 13, "peid": 18, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 74, "lid": 2, "pid": 13, "peid": 19, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 3, "channel": 5, "label_name": "我的酒会", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254823860bdfAf2sR8_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 14, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 14, "path_name": "我的酒会", "code": "MyWineParty", "client": "0,1,2,3", "ios_path": "MyRecepitonViewController", "android_path": "com.stg.rouge.activity.MyReceptionActivity", "mini_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "h5_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:51", "operator_name": "张红勇"}, "ad_path_param": [{"id": 109, "lid": 3, "pid": 14, "peid": 20, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 4, "channel": 5, "label_name": "我的收藏", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254874154NhX2kHPEk_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 15, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 15, "path_name": "我的收藏", "code": "MyCollect", "client": "0,1,2,3", "ios_path": "MyCollectViewController", "android_path": "com.stg.rouge.activity.PersonCollectActivity", "mini_path": "/packageE/pages/my-collection/my-collection", "h5_path": "/packageE/pages/my-collection/my-collection", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:36:57", "operator_name": "张红勇"}, "ad_path_param": [{"id": 114, "lid": 4, "pid": 15, "peid": 21, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 5, "channel": 5, "label_name": "购物车", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255137820iedjK3nfP_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 19, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 19, "path_name": "购物车", "code": "shopCar", "client": "0,1,2,3", "ios_path": "ShoppingCartViewController", "android_path": "com.stg.rouge.activity.ShopCarActivity", "mini_path": "/packageB/pages/shopping-cart/shopping-cart", "h5_path": "/packageB/pages/shopping-cart/shopping-cart", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:45:16", "operator_name": "张红勇"}, "ad_path_param": [{"id": 59, "lid": 5, "pid": 19, "peid": 27, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 6, "channel": 5, "label_name": "体验券", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/16522554126283j6Q6cezH_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 20, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 20, "path_name": "体验券", "code": "ExperienceVoucher", "client": "0,1,2", "ios_path": "MyExperienceVoucherViewController", "android_path": "com.stg.rouge.activity.ExperienceVoucherActivity", "mini_path": "/packageE/pages/experience-coupon-list/experience-coupon-list", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:47:38", "operator_name": "张红勇"}, "ad_path_param": [{"id": 116, "lid": 6, "pid": 20, "peid": 80, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "<PERSON><PERSON><PERSON><PERSON>", "h5_val": "1"}]}, {"id": 7, "channel": 5, "label_name": "在线客服", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255845705S4Dwp8QET_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 25, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 25, "path_name": "在线客服", "code": "kefu", "client": "0,1", "ios_path": "ordinarykefu", "android_path": "zaixian.kefu", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:56:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 135, "lid": 7, "pid": 25, "peid": 34, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 23, "channel": 5, "label_name": "申请认证", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656468807755TQf3tZa5T_申请认证.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 17, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 17, "path_name": "申请认证", "code": "ApplyCertify", "client": "0,1,2,3", "ios_path": "applyCertificationViewController", "android_path": "com.stg.rouge.activity.CertifyActivity", "mini_path": "/packageE/pages/certification-apply/certification-apply", "h5_path": "/packageE/pages/certification-apply/certification-apply", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:41:48", "operator_name": "张红勇"}, "ad_path_param": [{"id": 118, "lid": 23, "pid": 17, "peid": 25, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 9, "channel": 5, "label_name": "收货地址", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255983366GwkfWA8jG_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 18, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 18, "path_name": "收货地址", "code": "AddressManage", "client": "0,1,2,3", "ios_path": "AddressManagementViewController", "android_path": "com.stg.rouge.activity.AddressManageActivity", "mini_path": "/packageE/pages/address-management/address-management", "h5_path": "/packageE/pages/address-management/address-management", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:42:40", "operator_name": "张红勇"}, "ad_path_param": [{"id": 125, "lid": 9, "pid": 18, "peid": 26, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}], "total": 9}}