{"client_0_channel_0": {"list": [{"id": 33, "channel": 0, "label_name": "拍卖", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230210/1675992112149bZTKnFtB2_xH2C3Dh2w.gif?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,gif", "param": null, "badge": "", "path_id": 55, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 55, "path_name": "拍卖首页", "code": "AuctionIndex", "client": "0,1,3", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "", "h5_path": "/packageH/pages/auction-index/auction-index", "pc_path": "", "param": null, "operator_id": 84, "created_at": "2023-01-17 18:56:04", "operator_name": "甘高函"}, "ad_path_param": [{"id": 520, "lid": 33, "pid": 55, "peid": 94, "title": "路径", "ios_key": "url", "ios_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index", "android_key": "url", "android_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index", "mini_key": "", "mini_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index", "h5_key": "", "h5_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index"}, {"id": 521, "lid": 33, "pid": 55, "peid": 95, "title": "状态栏", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "isHideTitle", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 522, "lid": 33, "pid": 55, "peid": 96, "title": "额外功能", "ios_key": "useType", "ios_val": "1", "android_key": "useType", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 523, "lid": 33, "pid": 55, "peid": 97, "title": "标题栏", "ios_key": "isHideNav", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": null, "mini_val": "", "h5_key": null, "h5_val": ""}]}, {"id": 41, "channel": 0, "label_name": "国酒云集", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230303/1677823571378yE34hXGYm_E2TriaKy7.png?w=792&h=792&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 525, "lid": 41, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor"}, {"id": 526, "lid": 41, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 527, "lid": 41, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 12, "channel": 0, "label_name": "精品酒会", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230221/1676960399393SY7anMMRZ_4MZRQSTKX.png?w=397&h=397&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 26, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 26, "path_name": "精品酒会", "code": "ReceptionList", "client": "0,1,2,3", "ios_path": "FineReceptionListViewController", "android_path": "com.stg.rouge.activity.ReceptionListActivity", "mini_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "h5_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-12 18:19:37", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 24, "channel": 0, "label_name": "跨境尾货", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230217/1676622062642tbfth88n7_JaRbjwdZ2.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 480, "lid": 24, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/kuajing", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/kuajing", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/kuajing", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/kuajing"}, {"id": 481, "lid": 24, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 482, "lid": 24, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 37, "channel": 0, "label_name": "挑了狠酒", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230228/1677548983714Kh8f6Gswm_5jKpr8wxj.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 499, "lid": 37, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/chooseWine", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/chooseWine", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/chooseWine", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/chooseWine"}, {"id": 500, "lid": 37, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 501, "lid": 37, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 26, "channel": 0, "label_name": "拍照识酒", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/1674005467874x2tB6eQP3_FFGcKGx7A.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 54, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 54, "path_name": "拍照识酒", "code": "TakePicturesKnowWine", "client": "0,1,2", "ios_path": "DBCameraContainerViewController", "android_path": "com.stg.rouge.activity.KnowWineActivity", "mini_path": "jumpScanWineMini", "h5_path": "", "pc_path": "", "param": null, "operator_id": 254, "created_at": "2022-10-20 10:58:30", "operator_name": "轩艺畅"}, "ad_path_param": [{"id": 452, "lid": 26, "pid": 54, "peid": 93, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 28, "channel": 0, "label_name": "醉心烈酒", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230217/16766220803915YwCCB47W_NGrHjSndE.png?w=397&h=397&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 516, "lid": 28, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday"}, {"id": 517, "lid": 28, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 518, "lid": 28, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 30, "channel": 0, "label_name": "每日任务", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/1674005436796pxWr5e5y8_satRBe6nT.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 11, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 11, "path_name": "今日任务", "code": "EveryTask", "client": "0,1,2,3", "ios_path": "DailyCheckViewController", "android_path": "com.stg.rouge.activity.EveryTaskCenterActivity", "mini_path": "/packageE/pages/daily-tasks/daily-tasks", "h5_path": "/packageE/pages/daily-tasks/daily-tasks", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:32:29", "operator_name": "张红勇"}, "ad_path_param": [{"id": 494, "lid": 30, "pid": 11, "peid": 15, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 32, "channel": 0, "label_name": "酒闻", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/16740061192544w6tXpEdy_4djknWDaf.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 31, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 31, "path_name": "酒闻资讯", "code": "WinesmellList", "client": "0,1,2,3", "ios_path": "SmellWineListViewController", "android_path": "com.stg.rouge.activity.WinesmellListActivity", "mini_path": "/packageD/pages/wine-smell-news/wine-smell-news", "h5_path": "/packageD/pages/wine-smell-news/wine-smell-news", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-24 09:51:39", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 13, "channel": 0, "label_name": "社区", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/1674005478320Xa3J7y2eD_mMWcKYniW.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 45, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 45, "path_name": "首页发现", "code": "MainFind", "client": "0,1", "ios_path": "go<PERSON>ain", "android_path": "go<PERSON>ain", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-06-13 14:18:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 404, "lid": 13, "pid": 45, "peid": 70, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "2", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}], "total": 10}, "client_0_channel_1": {"list": [], "total": 0}, "client_0_channel_2": {"list": [], "total": 0}, "client_0_channel_3": {"list": [], "total": 0}, "client_0_channel_4": {"list": [], "total": 0}, "client_0_channel_5": {"list": [{"id": 5, "channel": 5, "label_name": "购物车", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666234131903xtXpjBd4Y_6QN5r8i2m.png?w=66&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 19, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 19, "path_name": "购物车", "code": "shopCar", "client": "0,1,2,3", "ios_path": "ShoppingCartViewController", "android_path": "com.stg.rouge.activity.ShopCarActivity", "mini_path": "/packageB/pages/shopping-cart/shopping-cart", "h5_path": "/packageB/pages/shopping-cart/shopping-cart", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:45:16", "operator_name": "张红勇"}, "ad_path_param": [{"id": 347, "lid": 5, "pid": 19, "peid": 27, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 3, "channel": 5, "label_name": "酒会订单", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666234155877XtG3WQR7f_atXECQW5K.png?w=69&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 14, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 14, "path_name": "我的酒会", "code": "MyWineParty", "client": "0,1,2,3", "ios_path": "MyRecepitonViewController", "android_path": "com.stg.rouge.activity.MyReceptionActivity", "mini_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "h5_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:51", "operator_name": "张红勇"}, "ad_path_param": [{"id": 248, "lid": 3, "pid": 14, "peid": 20, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 4, "channel": 5, "label_name": "我的收藏", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666234183082EdKh3Edfm_ZX7WaCNbd.png?w=69&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 15, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 15, "path_name": "我的收藏", "code": "MyCollect", "client": "0,1,2,3", "ios_path": "MyCollectViewController", "android_path": "com.stg.rouge.activity.PersonCollectActivity", "mini_path": "/packageE/pages/my-collection/my-collection", "h5_path": "/packageE/pages/my-collection/my-collection", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:36:57", "operator_name": "张红勇"}, "ad_path_param": [{"id": 249, "lid": 4, "pid": 15, "peid": 21, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 25, "channel": 5, "label_name": "售后记录", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666231390837C5B7Kbe8W_SYk8YGHwG.png?w=66&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 53, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 53, "path_name": "售后记录", "code": "AfterOrder", "client": "0,1,2,3", "ios_path": "AfterSalesListViewController", "android_path": "com.stg.rouge.activity.AfterOrderListActivity", "mini_path": "/packageE/pages/my-order/my-order", "h5_path": "/packageE/pages/my-order/my-order", "pc_path": "", "param": null, "operator_id": 254, "created_at": "2022-10-20 10:02:29", "operator_name": "轩艺畅"}, "ad_path_param": [{"id": 489, "lid": 25, "pid": 53, "peid": 91, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}, {"id": 490, "lid": 25, "pid": 53, "peid": 92, "title": "页面参数1", "ios_key": "sub_order_status", "ios_val": "6", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "status", "mini_val": "6", "h5_key": "status", "h5_val": "6"}]}, {"id": 7, "channel": 5, "label_name": "在线客服", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230225/1677327110723smdpykGAc_Qf7fscpbr.png?w=44&h=44&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 25, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 25, "path_name": "在线客服", "code": "kefu", "client": "0,1", "ios_path": "ordinarykefu", "android_path": "zaixian.kefu", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:56:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 488, "lid": 7, "pid": 25, "peid": 34, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 36, "channel": 5, "label_name": "心愿清单", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230225/1677327018227A2Eik3Dpd_hShpy8cJm.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 58, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 58, "path_name": "心愿清单列表", "code": "WishList", "client": "0,1,2,3", "ios_path": "WishListViewController", "android_path": "com.stg.rouge.activity.WishListActivity", "mini_path": "/packageE/pages/wish-list/wish-list", "h5_path": "/packageE/pages/wish-list/wish-list", "pc_path": "", "param": null, "operator_id": 318, "created_at": "2023-02-25 19:05:53", "operator_name": "黄金"}, "ad_path_param": [{"id": 487, "lid": 36, "pid": 58, "peid": 104, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 11, "channel": 5, "label_name": "我的视频", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656467810605GJmrXYMmJ_我的视频.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 12, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 12, "path_name": "我的视频", "code": "MyVideo", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:34:20", "operator_name": "张红勇"}, "ad_path_param": [{"id": 238, "lid": 11, "pid": 12, "peid": 16, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 239, "lid": 11, "pid": 12, "peid": 17, "title": "页面参数", "ios_key": "type", "ios_val": "1", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 2, "channel": 5, "label_name": "我的帖子", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254625003d3pb8w3tH_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 13, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 13, "path_name": "我的帖子", "code": "MyPost", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:03", "operator_name": "张红勇"}, "ad_path_param": [{"id": 240, "lid": 2, "pid": 13, "peid": 18, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 241, "lid": 2, "pid": 13, "peid": 19, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 9, "channel": 5, "label_name": "收货地址", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255983366GwkfWA8jG_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 18, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 18, "path_name": "收货地址", "code": "AddressManage", "client": "0,1,2,3", "ios_path": "AddressManagementViewController", "android_path": "com.stg.rouge.activity.AddressManageActivity", "mini_path": "/packageE/pages/address-management/address-management", "h5_path": "/packageE/pages/address-management/address-management", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:42:40", "operator_name": "张红勇"}, "ad_path_param": [{"id": 251, "lid": 9, "pid": 18, "peid": 26, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 23, "channel": 5, "label_name": "申请认证", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656468807755TQf3tZa5T_申请认证.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 17, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 17, "path_name": "申请认证", "code": "ApplyCertify", "client": "0,1,2,3", "ios_path": "applyCertificationViewController", "android_path": "com.stg.rouge.activity.CertifyActivity", "mini_path": "/packageE/pages/certification-apply/certification-apply", "h5_path": "/packageE/pages/certification-apply/certification-apply", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:41:48", "operator_name": "张红勇"}, "ad_path_param": [{"id": 243, "lid": 23, "pid": 17, "peid": 25, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}], "total": 10}, "client_1_channel_0": {"list": [{"id": 33, "channel": 0, "label_name": "拍卖", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230210/1675992112149bZTKnFtB2_xH2C3Dh2w.gif?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,gif", "param": null, "badge": "", "path_id": 55, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 55, "path_name": "拍卖首页", "code": "AuctionIndex", "client": "0,1,3", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "", "h5_path": "/packageH/pages/auction-index/auction-index", "pc_path": "", "param": null, "operator_id": 84, "created_at": "2023-01-17 18:56:04", "operator_name": "甘高函"}, "ad_path_param": [{"id": 520, "lid": 33, "pid": 55, "peid": 94, "title": "路径", "ios_key": "url", "ios_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index", "android_key": "url", "android_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index", "mini_key": "", "mini_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index", "h5_key": "", "h5_val": "https://h5.vinehoo.com/packageH/pages/auction-index/auction-index"}, {"id": 521, "lid": 33, "pid": 55, "peid": 95, "title": "状态栏", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "isHideTitle", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 522, "lid": 33, "pid": 55, "peid": 96, "title": "额外功能", "ios_key": "useType", "ios_val": "1", "android_key": "useType", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 523, "lid": 33, "pid": 55, "peid": 97, "title": "标题栏", "ios_key": "isHideNav", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": null, "mini_val": "", "h5_key": null, "h5_val": ""}]}, {"id": 41, "channel": 0, "label_name": "国酒云集", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230303/1677823571378yE34hXGYm_E2TriaKy7.png?w=792&h=792&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 525, "lid": 41, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/nationalLiquor"}, {"id": 526, "lid": 41, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 527, "lid": 41, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 12, "channel": 0, "label_name": "精品酒会", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230221/1676960399393SY7anMMRZ_4MZRQSTKX.png?w=397&h=397&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 26, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 26, "path_name": "精品酒会", "code": "ReceptionList", "client": "0,1,2,3", "ios_path": "FineReceptionListViewController", "android_path": "com.stg.rouge.activity.ReceptionListActivity", "mini_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "h5_path": "/packageD/pages/wine-party-boutique/wine-party-boutique", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-12 18:19:37", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 24, "channel": 0, "label_name": "跨境尾货", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230217/1676622062642tbfth88n7_JaRbjwdZ2.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 480, "lid": 24, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/kuajing", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/kuajing", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/kuajing", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/kuajing"}, {"id": 481, "lid": 24, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 482, "lid": 24, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 37, "channel": 0, "label_name": "挑了狠酒", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230228/1677548983714Kh8f6Gswm_5jKpr8wxj.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 499, "lid": 37, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/chooseWine", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/chooseWine", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/chooseWine", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/chooseWine"}, {"id": 500, "lid": 37, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 501, "lid": 37, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 26, "channel": 0, "label_name": "拍照识酒", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/1674005467874x2tB6eQP3_FFGcKGx7A.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 54, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 54, "path_name": "拍照识酒", "code": "TakePicturesKnowWine", "client": "0,1,2", "ios_path": "DBCameraContainerViewController", "android_path": "com.stg.rouge.activity.KnowWineActivity", "mini_path": "jumpScanWineMini", "h5_path": "", "pc_path": "", "param": null, "operator_id": 254, "created_at": "2022-10-20 10:58:30", "operator_name": "轩艺畅"}, "ad_path_param": [{"id": 452, "lid": 26, "pid": 54, "peid": 93, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 28, "channel": 0, "label_name": "醉心烈酒", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230217/16766220803915YwCCB47W_NGrHjSndE.png?w=397&h=397&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 30, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 30, "path_name": "网页状态栏由客户端处理", "code": "webNoTitle", "client": "0,1,2", "ios_path": "GeneralWebViewController", "android_path": "com.stg.rouge.webview.WebActivity", "mini_path": "miniJumpWebView", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-17 17:12:43", "operator_name": "张红勇"}, "ad_path_param": [{"id": 516, "lid": 28, "pid": 30, "peid": 45, "title": "url", "ios_key": "url", "ios_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday", "android_key": "url", "android_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday", "mini_key": "url", "mini_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday", "h5_key": "", "h5_val": "https://activity.vinehoo.com/activities-v3/liquorHoliday"}, {"id": 517, "lid": 28, "pid": 30, "peid": 46, "title": "页面参数1", "ios_key": "isHideNav", "ios_val": "NO", "android_key": "isHideTitle ", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 518, "lid": 28, "pid": 30, "peid": 47, "title": "页面参数2", "ios_key": "isStatusBar", "ios_val": "yes", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 30, "channel": 0, "label_name": "每日任务", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/1674005436796pxWr5e5y8_satRBe6nT.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 11, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 11, "path_name": "今日任务", "code": "EveryTask", "client": "0,1,2,3", "ios_path": "DailyCheckViewController", "android_path": "com.stg.rouge.activity.EveryTaskCenterActivity", "mini_path": "/packageE/pages/daily-tasks/daily-tasks", "h5_path": "/packageE/pages/daily-tasks/daily-tasks", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:32:29", "operator_name": "张红勇"}, "ad_path_param": [{"id": 494, "lid": 30, "pid": 11, "peid": 15, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 32, "channel": 0, "label_name": "酒闻", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/16740061192544w6tXpEdy_4djknWDaf.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 31, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 31, "path_name": "酒闻资讯", "code": "WinesmellList", "client": "0,1,2,3", "ios_path": "SmellWineListViewController", "android_path": "com.stg.rouge.activity.WinesmellListActivity", "mini_path": "/packageD/pages/wine-smell-news/wine-smell-news", "h5_path": "/packageD/pages/wine-smell-news/wine-smell-news", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-24 09:51:39", "operator_name": "张红勇"}, "ad_path_param": []}, {"id": 13, "channel": 0, "label_name": "社区", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230118/1674005478320Xa3J7y2eD_mMWcKYniW.png?w=396&h=396&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 45, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "首页", "client_path": {"id": 45, "path_name": "首页发现", "code": "MainFind", "client": "0,1", "ios_path": "go<PERSON>ain", "android_path": "go<PERSON>ain", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-06-13 14:18:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 404, "lid": 13, "pid": 45, "peid": 70, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "2", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}], "total": 10}, "client_1_channel_1": {"list": [], "total": 0}, "client_1_channel_2": {"list": [], "total": 0}, "client_1_channel_3": {"list": [], "total": 0}, "client_1_channel_4": {"list": [], "total": 0}, "client_1_channel_5": {"list": [{"id": 5, "channel": 5, "label_name": "购物车", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666234131903xtXpjBd4Y_6QN5r8i2m.png?w=66&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 19, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 19, "path_name": "购物车", "code": "shopCar", "client": "0,1,2,3", "ios_path": "ShoppingCartViewController", "android_path": "com.stg.rouge.activity.ShopCarActivity", "mini_path": "/packageB/pages/shopping-cart/shopping-cart", "h5_path": "/packageB/pages/shopping-cart/shopping-cart", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:45:16", "operator_name": "张红勇"}, "ad_path_param": [{"id": 347, "lid": 5, "pid": 19, "peid": 27, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 3, "channel": 5, "label_name": "酒会订单", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666234155877XtG3WQR7f_atXECQW5K.png?w=69&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 14, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 14, "path_name": "我的酒会", "code": "MyWineParty", "client": "0,1,2,3", "ios_path": "MyRecepitonViewController", "android_path": "com.stg.rouge.activity.MyReceptionActivity", "mini_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "h5_path": "/packageD/pages/wine-party-order-list/wine-party-order-list", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:51", "operator_name": "张红勇"}, "ad_path_param": [{"id": 248, "lid": 3, "pid": 14, "peid": 20, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 4, "channel": 5, "label_name": "我的收藏", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666234183082EdKh3Edfm_ZX7WaCNbd.png?w=69&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 15, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 15, "path_name": "我的收藏", "code": "MyCollect", "client": "0,1,2,3", "ios_path": "MyCollectViewController", "android_path": "com.stg.rouge.activity.PersonCollectActivity", "mini_path": "/packageE/pages/my-collection/my-collection", "h5_path": "/packageE/pages/my-collection/my-collection", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:36:57", "operator_name": "张红勇"}, "ad_path_param": [{"id": 249, "lid": 4, "pid": 15, "peid": 21, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 25, "channel": 5, "label_name": "售后记录", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/2052/1666231390837C5B7Kbe8W_SYk8YGHwG.png?w=66&h=66&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 53, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 53, "path_name": "售后记录", "code": "AfterOrder", "client": "0,1,2,3", "ios_path": "AfterSalesListViewController", "android_path": "com.stg.rouge.activity.AfterOrderListActivity", "mini_path": "/packageE/pages/my-order/my-order", "h5_path": "/packageE/pages/my-order/my-order", "pc_path": "", "param": null, "operator_id": 254, "created_at": "2022-10-20 10:02:29", "operator_name": "轩艺畅"}, "ad_path_param": [{"id": 489, "lid": 25, "pid": 53, "peid": 91, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}, {"id": 490, "lid": 25, "pid": 53, "peid": 92, "title": "页面参数1", "ios_key": "sub_order_status", "ios_val": "6", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "status", "mini_val": "6", "h5_key": "status", "h5_val": "6"}]}, {"id": 7, "channel": 5, "label_name": "在线客服", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230225/1677327110723smdpykGAc_Qf7fscpbr.png?w=44&h=44&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 25, "page_area": 1, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 25, "path_name": "在线客服", "code": "kefu", "client": "0,1", "ios_path": "ordinarykefu", "android_path": "zaixian.kefu", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:56:05", "operator_name": "张红勇"}, "ad_path_param": [{"id": 488, "lid": 7, "pid": 25, "peid": 34, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 36, "channel": 5, "label_name": "心愿清单", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20230225/1677327018227A2Eik3Dpd_hShpy8cJm.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 58, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 58, "path_name": "心愿清单列表", "code": "WishList", "client": "0,1,2,3", "ios_path": "WishListViewController", "android_path": "com.stg.rouge.activity.WishListActivity", "mini_path": "/packageE/pages/wish-list/wish-list", "h5_path": "/packageE/pages/wish-list/wish-list", "pc_path": "", "param": null, "operator_id": 318, "created_at": "2023-02-25 19:05:53", "operator_name": "黄金"}, "ad_path_param": [{"id": 487, "lid": 36, "pid": 58, "peid": 104, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 11, "channel": 5, "label_name": "我的视频", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656467810605GJmrXYMmJ_我的视频.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 12, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 12, "path_name": "我的视频", "code": "MyVideo", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:34:20", "operator_name": "张红勇"}, "ad_path_param": [{"id": 238, "lid": 11, "pid": 12, "peid": 16, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 239, "lid": 11, "pid": 12, "peid": 17, "title": "页面参数", "ios_key": "type", "ios_val": "1", "android_key": "<PERSON><PERSON><PERSON><PERSON>", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 2, "channel": 5, "label_name": "我的帖子", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652254625003d3pb8w3tH_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 13, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 13, "path_name": "我的帖子", "code": "MyPost", "client": "0,1", "ios_path": "PostsAndVideoViewController", "android_path": "com.stg.rouge.activity.UserHomeActivity", "mini_path": "", "h5_path": "", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:35:03", "operator_name": "张红勇"}, "ad_path_param": [{"id": 240, "lid": 2, "pid": 13, "peid": 18, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}, {"id": 241, "lid": 2, "pid": 13, "peid": 19, "title": "页面参数", "ios_key": "type", "ios_val": "2", "android_key": "type", "android_val": "1", "mini_key": "", "mini_val": "", "h5_key": "", "h5_val": ""}]}, {"id": 9, "channel": 5, "label_name": "收货地址", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/1652255983366GwkfWA8jG_编组 <EMAIL>?x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 18, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 18, "path_name": "收货地址", "code": "AddressManage", "client": "0,1,2,3", "ios_path": "AddressManagementViewController", "android_path": "com.stg.rouge.activity.AddressManageActivity", "mini_path": "/packageE/pages/address-management/address-management", "h5_path": "/packageE/pages/address-management/address-management", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:42:40", "operator_name": "张红勇"}, "ad_path_param": [{"id": 251, "lid": 9, "pid": 18, "peid": 26, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}, {"id": 23, "channel": 5, "label_name": "申请认证", "icon": "https://images.vinehoo.com/vinehoo/vos/marketing/20220629/1656468807755TQf3tZa5T_申请认证.png?w=74&h=74&x-oss-process=image/resize,w_84,h_84/auto-orient,1/quality,q_90/format,png", "param": null, "badge": "", "path_id": 17, "page_area": 2, "created_at": "1970-01-01 08:00:00", "channel_name": "个人中心", "client_path": {"id": 17, "path_name": "申请认证", "code": "ApplyCertify", "client": "0,1,2,3", "ios_path": "applyCertificationViewController", "android_path": "com.stg.rouge.activity.CertifyActivity", "mini_path": "/packageE/pages/certification-apply/certification-apply", "h5_path": "/packageE/pages/certification-apply/certification-apply", "pc_path": "", "param": null, "operator_id": 13, "created_at": "2022-05-11 15:41:48", "operator_name": "张红勇"}, "ad_path_param": [{"id": 243, "lid": 23, "pid": 17, "peid": 25, "title": "检查登录", "ios_key": "login", "ios_val": "1", "android_key": "login", "android_val": "1", "mini_key": "login", "mini_val": "1", "h5_key": "login", "h5_val": "1"}]}], "total": 10}}