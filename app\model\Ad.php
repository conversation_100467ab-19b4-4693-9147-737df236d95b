<?php

namespace app\model;

use app\model\ClientPathExtends as pathExtendsMol;
use app\service\v3\AdExternal;

class Ad extends Base
{
    protected $name = 'ad';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';


    /**
     * 客户端处理
     * @param $value
     * @param $data
     * @return array
     */
    public function getClientAttr($value,$data)
    {
        $arr = [];
        if($value || $value == 0){
            $adClientArr = explode(',',$value);//客户端数组 当前数据
            $client = config('config.ad_client');//所有客户端

            foreach ($client as $v){//获取客户端名称
                if(in_array($v['id'],$adClientArr)){
                    $arr[] = $v;
                }
            }
        }
        return $arr;
    }

    /**
     * 广告频道
     * @param $value
     * @param $data
     * @return array
     */
    public function getChannelAttr($value,$data)
    {
        $arr = [];
        if($value || $value == 0){
            $adClientArr = explode(',',$value);//频道数组 当前数据
            $client = config('config.ad_capsule_channel');//所有频道
            foreach ($client as $v){//获取客户端名称
                if(in_array($v['id'],$adClientArr)){
                    $arr[] = $v;
                }
            }
        }
        return $arr;
    }

    public function getClientPathAttr($value,$data)
    {
        return ClientPath::where('id',$data['path'])->find();
    }

    public function getImageAttr($value,$data)
    {
        if($value){
            return env("ALIURL").$value;
        }
        return '';
    }

    //竖图
    public function getVerticalImageAttr($value,$data)
    {
        if($value){
            return env("ALIURL").$value;
        }
        return '';
    }


    public function getOperatorIdAttr($value,$data)
    {

        return $value;
    }

    public function getOperatorNameAttr($value,$data)
    {
        return $value;
    }

    public function getPatternNameAttr($value, $data)
    {
        $pattern_texts = [];
        if ($data['pattern']) {
            $client      = config('config.ad_pattern');//所有频道
            $pattern_ids = explode(',', $data['pattern']);

            foreach ($pattern_ids as $pattern_id) {
                foreach ($client as $v) {//获取客户端名称
                    if ($v['id'] == $pattern_id) {
                        $pattern_texts[] = $v['name'];
                    }
                }
            }
        }
        return implode('、', $pattern_texts);
    }

    public function getModulDataAttr($value,$data)
    {

        if($data['modul_id'] && $data['modul']){

            switch ($data['modul']){
                case 1:
                    return AdExternal::getWineParty($data['modul_id']);
                    break;
                case 2:
                    return AdExternal::getLiveDetail($data['modul_id']);
                    break;
                case 3:
                    return AdExternal::getActivityDetail($data['modul_id']);
                    break;
                case 4:
                    return AdExternal::getGoodDetail($data['modul_id']);
                    break;
            }
        }
        return null;
    }

    public function setClientAttr($value,$data)
    {
        return implode(',',$value);
    }

    public function setChannelAttr($value,$data)
    {
        return implode(',',$value);
    }

    public function adPathParam() {
        return $this->hasMany(AdPathExtends::class, 'aid', 'id');
    }

    //返回广告参数
    public function getParamsAttr($value,$data) {

        return AdPathExtends::where('aid',$data['id'])->find();
//        return $this->hasMany(AdPathExtends::class, 'aid', 'id');
    }

    //获取综合状态
    public function getGatherStatusAttr($value,$data)
    {
        if(($data['start_time']<=time() && time()<=$data['end_time']) && $data['status'] == 1) return 1;
        return 0;
    }

    public function getAdPathValueParamAttr($value,$data) {

        if($data['path']) {
            $list = pathExtendsMol::field("id,title,pid")->whereRaw("pid=:pid and (ios_val='' and android_val='' and mini_val='' and h5_val='' )", ['pid' => $data['path']])->select()->toArray();

            $emptyvalarr = array_column($list,"id");

            $result = AdPathExtends::field("peid as id,title,ios_val as value")->whereIn('peid',$emptyvalarr)->where("aid",$data['id'])->select();
            return $result;
        }
        return [];
    }

    public function setStartTimeAttr($value,$data)
    {
        return strtotime($value);
    }

    public function getStartTimeAttr($value,$data)
    {
        return date("Y-m-d H:i:s",$value);
    }

    public function setEndTimeAttr($value,$data)
    {
        return strtotime($value);
    }
    
    public function getEndTimeAttr($value,$data)
    {
        return date("Y-m-d H:i:s",$value);
    }

}