<?php

namespace app\model;

use think\Model;

class Base extends Model
{
    // 搜索器
    public function searchScreenAttr($query, $value, $data) {
        if (isset($data['keyword']) && strlen($data['keyword']) > 0) {
            $query->where('title', 'like', '%' . $data['keyword'] . '%');
        }
        if (isset($data['cate_id']) && strlen($data['cate_id']) > 0) {
            $query->where('cate_id', $data['cate_id']);
        }
        if (isset($data['start_at']) && strlen($data['start_at']) > 0) {
            $query->where('created_time', '>', strtotime($data['start_at']));
        }
        if (isset($data['end_at']) && strlen($data['end_at']) > 0) {
            $query->where('created_time', '<', strtotime(date('Y-m-d 23:59:59', strtotime($data['end_at']))));
        }
    }

    /**
     * 查询
     * @param array $param
     * @param bool $fetchsql
     * @return mixed
     */
    static function getInf($param = [], $fetchsql = false) {
        // 字段
        if (isset($param['field'])) {
            $query = self::field($param['field']);
        } else {
            $query = self::field('*');
        }
        // 条件
        if (isset($param['where'])) {
            $query->where($param['where']);
        }
        // 条件
        if (isset($param['whereIn'])) {
            $query->whereIn($param['whereIn']['field'],$param['whereIn']['vaule']);
        }
        // 关联模型
        if (isset($param['with'])) {
            $query->with($param['with']);
        }
        // mysql函数排序
        if (isset($param['orderRaw'])) {
            $query->orderRaw($param['orderRaw']);
        }
        // 常规排序
        if (isset($param['order'])) {
            $query->order($param['order']);
        } else {
            $query->order('created_at', 'desc');
        }
        // 限制条数
        if (isset($param['limit'])) {
            $query->limit($param['limit'] ?? 15);
        }
        // 分页
        if (isset($param['page'])) {
            $query->page($param['page'] ?? 1);
        }
        // 是否加锁
        if (isset($param['lock'])) {
            $query->lock($param['lock']);
        }
        // 查询类型
        switch ($param['type']) {
            case 'find':
                return $fetchsql ? $query->fetchSql()->find() : $query->find();
            break;
            case 'select':
                return $fetchsql ? $query->fetchSql()->select() : $query->select();
            break;
            case 'count':
                return $query->count('id');
            break;
        }
    }
}
