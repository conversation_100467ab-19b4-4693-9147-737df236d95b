<?php

namespace app\model;

class Bullein extends Base
{
	protected $name = 'bullein';
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';

    public function setStartTimeAttr($value,$data)
    {
        return strtotime($value);
    }

    public function getStartTimeAttr($value,$data)
    {
        return date("Y-m-d H:i:s",$value);
    }

    public function setEndTimeAttr($value,$data)
    {
        return strtotime($value);
    }
    public function getEndTimeAttr($value,$data)
    {
        return date("Y-m-d H:i:s",$value);
    }

}