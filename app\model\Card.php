<?php

namespace app\model;

use app\service\v3\AdExternal;
use think\facade\Db;

class Card extends Base
{
	protected $name = 'card';

    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';

    // 关联客户端路径
    public function cardGoodLive() {
        return $this->hasMany(CardGoodsLive::class, 'cid', 'id');
    }

    public function getCardNameAttr($value, $data)
    {
//        if($data['card_class'] == 2) return env("ALIURL").$value;
        return $value;
    }

    public function getSubTitleBackAttr($value,$data)
    {
        return env("ALIURL").$value;
    }

    public function getShareImageAttr($value,$data)
    {
        return !empty($value) ? env("ALIURL") . $value : '';
    }


    public function getCardExtendDetailAttr($value,$data)
    {
        $id = $data['id'];
        //获取商品
        $result = CardGoodsLive::where('cid',$id)->where('status',1)->limit(0,10)->order(['sort' => 'desc', 'created_at' => 'desc'])->select()->toArray();

        $array_ids = array_column($result,'relation_id');
        $str_ids = implode(',',$array_ids);
        if($data['style'] == 7){
            $resouce = AdExternal::getLiveListByids($str_ids);//直播
        } else{
            $resouce = AdExternal::getGoodListByids($str_ids);//\r\n0:无缝横版滑动 \r\n1:有缝横版滑动 \r\n2:竖版滑动 \r\n3:白图滑动 \r\n4:特卖 \r\n5:秒杀 \r\n6:拼团 \r\n7:直播
        }

        $newresouce = $resouce['list'] ?? [];
        if($data['style'] != 7){//直播
            foreach ($newresouce as $k=>$value){
                if(isset($newresouce[$k]['product_img'])){
                    $productimg = explode(',',$value['product_img']);
                    $newresouce[$k]['product_img'] = env("ALIURL").$productimg[0];
                }else{
                    $newresouce[$k]['product_img'] = '';
                }
                $newresouce[$k]['banner_img'] = env("ALIURL").$value['banner_img'];
            }
        }

        $code = array_column($newresouce,null,'id');


        $rows = [];
        foreach ($result as $k=>$v){
            if($v['type'] && $v['relation_id'] && strlen($v['relation_id'])>0 && strlen($v['relation_id'])>0){
                switch ($v['type']){
                    case 1://商品
//                        $restresult = AdExternal::getGoodDetail($v['relation_id']);
                        $restresult = @$code[$v['relation_id']];
                        if($restresult){
                            if($restresult['onsale_status'] == 2 || $restresult['onsale_status'] == 1) {//返回在售中商品

                                if($data['pattern'] == 0) $restresult['title']= $v['title'];//横向滑动 修改卡片名称
                                if($data['pattern'] == 1) { //平铺展示
                                    $restresult['title']= $v['title'];//
                                    $restresult['product_img']= $v['image'];//
                                    $restresult['sub_title']= $v['sub_title'];//副标题
//                                    $restresult = array_merge($restresult, $v);// 修改卡片名称
                                }
                                array_push($rows, $restresult);
                            }
                        }else{
                            unset($result[$k]);
                        }
                        break;
                    case 2://直播
                        $restresult = AdExternal::getLiveDetail($v['relation_id']);
                        if($restresult){
                            $restresult['title']=$v['title'];
                            array_push($rows,$restresult);

//                            $result[$k] = $restresult;
                        }else{
                            unset($result[$k]);
                        }

                        break;
                }
            }

        }
        return $rows;
//        return $result;
    }

    // 批量获取卡片详情
    static function getCardExtendDetailBatch ($list)
    {
        $list_map = array_column($list,null,'id');
        $ids = array_column($list, 'id');
        //获取商品
        $result = CardGoodsLive::whereIn('cid', $ids)
            ->where('status', 1)
            ->order(['sort' => 'desc', 'created_at' => 'desc'])
            ->select()->toArray();
        $live_ids = $goods_ids = $goods_list = $card_goods_ids = [];
        foreach ($result as $v) {
            $card_info = $list_map[$v['cid']] ?? [];
            if (empty($card_info)) {
                continue;
            }

            $card_goods_ids[] = $v['id'];
            if ($card_info['style'] == 7) {//直播
                $live_ids[] = $v['relation_id'];
            } else {
                $goods_ids[] = $v['relation_id'];
            }
            $goods_list[$v['cid']][] = $v;
        }

        #筛选项
        $card_goods_filter = Db::name('card_goods_filter')
            ->alias('chf')
            ->leftJoin('card_filter cf','cf.id = chf.filter_id')
            ->whereIn('chf.goods_id', $card_goods_ids)
            ->order('cf.sort asc')
            ->column('goods_id,filter_id,cf.name');
        $filter_info = [];
        foreach ($card_goods_filter as $v) {
            $filter_info[$v['goods_id']][] = [
                'id' => $v['filter_id'],
                'name' => $v['name'],
            ];
        }

        
        $live_code = $goods_code = [];
        if (!empty($live_ids)) {//直播信息
            $resouce = AdExternal::getLiveListByids(implode(',', $live_ids));//直播
            $newresouce = $resouce['list'] ?? [];
            foreach ($newresouce as $k=>$value){
                if(isset($newresouce[$k]['product_img'])){
                    $productimg = explode(',',$value['product_img']);
                    $newresouce[$k]['product_img'] = env("ALIURL").$productimg[0];
                }else{
                    $newresouce[$k]['product_img'] = '';
                }
                $newresouce[$k]['banner_img'] = env("ALIURL").$value['banner_img'];
            }
            $live_code = array_column($newresouce,null,'id');
        }

        if (!empty($goods_ids)) {//商品信息
            $resouce = AdExternal::getGoodListByids(implode(',', $goods_ids));//\r\n0:无缝横版滑动 \r\n1:有缝横版滑动 \r\n2:竖版滑动 \r\n3:白图滑动 \r\n4:特卖 \r\n5:秒杀 \r\n6:拼团 \r\n7:直播
            $newresouce = $resouce['list'] ?? [];
            $goods_code = array_column($newresouce,null,'id');
        }

        foreach ($list as &$data) {
            $goods = $goods_list[$data['id']] ?? [];
            $min_price = $max_price = 0;
            $rows = [];
            foreach ($goods as $k => $v) {
                if (!empty($v['relation_id'])) {
                    switch ($v['type']){
                        case 1://商品
                            $restresult = $goods_code[$v['relation_id']] ?? [];
                            if (!empty($restresult)) {
                                $restresult['card_filter_info'] = $filter_info[$v['id']] ?? [];
                                //返回在售中商品
                                if (in_array($restresult['onsale_status'], [1, 2])) {
                                    // 最小价格
                                    if (!empty($restresult['card_filter_info']) && ($min_price == 0 || $min_price > $restresult['price'])) {
                                        $min_price = floatval($restresult['price']);
                                    }
                                    // 最大价格
                                    if (!empty($restresult['card_filter_info']) && ($max_price == 0 || $max_price < $restresult['price'])) {
                                        $max_price = floatval($restresult['price']);
                                    }

                                    if($data['pattern'] == 0) $restresult['title']= $v['title'];//横向滑动 修改卡片名称
                                    if($data['pattern'] == 1) { //平铺展示
                                        $restresult['title']= $v['title'];//
                                        $restresult['product_img']= $v['image'];//
                                        $restresult['sub_title']= $v['sub_title'];//副标题
                                    }
                                    array_push($rows, $restresult);
                                }
                            }else{
                                unset($result[$k]);
                            }
                            break;
                        case 2://直播
                            $restresult = $live_code[$v['relation_id']] ?? [];//AdExternal::getLiveDetail($v['relation_id']);
                            if (!empty($restresult)) {
                                $restresult['title']=$v['title'];
                                array_push($rows,$restresult);
                            }else{
                                unset($result[$k]);
                            }
    
                            break;
                    }
                }
            }
            $data['min_price'] = $min_price;
            $data['max_price'] = $max_price;
            $data['card_extend_detail'] = $rows;
        }

        return $list;
    }

    public function getCardDataNumsAttr($value,$data)
    {
        return CardGoodsLive::where("cid",$data['id'])->count();
    }

    public function getAutoAddContentAttr($value)
    {
        return empty($value) ? [] : json_decode($value,true);
    }

    public function setAutoAddContentAttr($value)
    {
        if (is_array($value)) {
            // 数组值转字符串再转json
            foreach ($value as $k => &$v) {
                $value[$k] = [
                    'id' => intval($v['id']),
                    'name' => strval($v['name']),
                ];
            }
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    public function setShareImageAttr($value,$data)
    {
        return !empty($value) ? str_replace(env("ALIURL"), '', $value) : '';
    }
}