<?php

namespace app\model;

use app\service\v3\AdExternal;

class Column extends Base
{
	protected $name = 'column';

    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_at';
    protected $updateTime = 'update_time';

    // 关联客户端路径
    public function columnGoods() {
        return $this->hasMany(ColumnGoods::class, 'cid', 'id');
    }


    public function getGoodsNumsAttr($value,$data)
    {
        return ColumnGoods::where("cid",$data['id'])->count();
    }

    public function setCreatedAtAttr($value)
    {
        return empty($value) ? time() : strtotime($value);
    }

    public function getAutoAddContentAttr($value)
    {
        return empty($value) ? [] : json_decode($value, true);
    }

    public function setAutoAddContentAttr($value)
    {
        if (is_array($value)) {
            // 数组值转字符串再转json
            foreach ($value as $k => &$v) {
                $value[$k] = [
                    'id' => intval($v['id']),
                    'name' => strval($v['name']),
                ];
            }
            $value = json_encode($value, JSON_UNESCAPED_UNICODE);
        }
        return $value;
    }

    public function getIconAttr($value)
    {
        if (empty($value)) {
            return '';
        }
        if (strpos($value, 'http') !== false) {
            return $value;
        }
        return env("ALIURL") . $value;
    }

    public function setIconAttr($value)
    {
        if (strpos($value, env("ALIURL")) !== false) {
            return str_replace(env("ALIURL"), '', $value);
        } else {
            return $value;
        }
    }

    public function getBadgeAttr($value)
    {
        if (empty($value)) {
            return '';
        }
        if (strpos($value, 'http') !== false) {
            return $value;
        }
        return env("ALIURL") . $value;
    }

    public function setBadgeAttr($value)
    {
        if (strpos($value, env("ALIURL")) !== false) {
            return str_replace(env("ALIURL"), '', $value);
        } else {
            return $value;
        }

    }

    public function getClientAttr($value)
    {
        return empty($value) ? [] : explode(',', $value);
    }

    public function setClientAttr($value)
    {
        if (!empty($value) && is_array($value)) {
            return implode(',', $value);
        }
        return $value;
    }

    public function getPathAttr($value,$data)
    {
        return '/activities-v3/ColumnActivity?id=' . $data['id'];
    }

    public function setTopImageAttr($value)
    {
        if (strpos($value, env("ALIURL")) !== false) {
            return str_replace(env("ALIURL"), '', $value);
        } else {
            return $value;
        }
    }

    public function getTopImageAttr($value)
    {
        if (empty($value)) {
            return '';
        }
        if (strpos($value, 'http') !== false) {
            return $value;
        }
        return env("ALIURL") . $value;
    }

}