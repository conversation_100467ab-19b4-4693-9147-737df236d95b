<?php

namespace app\model;

class ColumnFilter extends Base
{
	protected $name = 'column_filter';
    protected $createTime = 'created_at';
    protected $updateTime = 'update_at';

    public function getCreatedAtAttr($value)
    {
        return empty($value) ? '' : date('Y-m-d H:i:s', $value);
    }

    public function getUpdateAtAttr($value)
    {
        return empty($value) ? '' : date('Y-m-d H:i:s', $value);
    }

    public function getAutoAddContentAttr($value)
    {
        return !empty($value) ? json_decode($value, true) : [];
    }

    public function setAutoAddContentAttr($value)
    {
        return !empty($value) ? json_encode($value) : '';
    }
}