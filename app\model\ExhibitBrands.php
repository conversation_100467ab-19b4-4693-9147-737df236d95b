<?php

namespace app\model;

use think\facade\Log;

class ExhibitBrands extends BaseModel
{
    protected $append = ['exhibition_name', 'booth_name', 'booth_no'];


//    #region 更新时间 获取器和编辑器
//    public function setUpdatedAtAttr($value)
//    {
//        if (!$value) return null;
//        if (is_numeric($value)) return $value;
//        return $value ? strtotime($value) : null;
//    }
//
//    public function getUpdatedAtAttr($value)
//    {
//        return $value ? date('Y-m-d H:i:s',$value) : null;
//    }
//    #endregion


    public function getIsOwnBrandAttr($value)
    {
        return boolval($value);
    }

    public function setIsOwnBrandAttr($value)
    {
        return intval($value);
    }

    public function getTopImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getBrandBottomImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getBrandAboutSectionImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getLogoUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }


    public function getExhibitionNameAttr($value, $row)
    {
        return ExhibitExhibitions::where('id', $row['exhibition_id'])->value('name');
    }

    public function getBoothNameAttr($value, $row)
    {
        return ExhibitBooths::where('id', $row['booth_id'])->value('name');
    }

    public function getBoothNoAttr($value, $row)
    {
        return ExhibitBooths::where('id', $row['booth_id'])->value('number');
    }

    public static function onAfterWrite($model)
    {
        ExhibitProducts::where('brand_id', $model->id)->update([
            'exhibition_id' => $model->exhibition_id,
            'booth_id'      => $model->booth_id
        ]);
        $model->furshExhib($model);
        $model->furshBonth($model);
        (new \CDN())->refresh($model->exhibition_id, $model->booth_id);
    }

    public static function onAfterDelete($model)
    {
        $model->furshExhib($model);
        $model->furshBonth($model);
        (new \CDN())->refresh($model->exhibition_id, $model->booth_id);
    }


    public function furshExhib($model)
    {
        try {
            $exhibition = ExhibitExhibitions::where('id', $model->exhibition_id)->find();
            if (!empty($exhibition)) {
                $exhibition = $exhibition->toArray();
            }
            $featured = ExhibitProducts::where('is_featured', 1)->where('exhibition_id', $model->exhibition_id)->order('sort', 'DESC')->select()->toArray();
            $brands   = ExhibitBrands::where('exhibition_id', $model->exhibition_id)->order('sort', 'DESC')->select()->toArray();

            $data = [
                'error_code' => 0,
                'error_msg'  => '',
                'data'       => compact('exhibition', 'featured', 'brands')
            ];

            $path     = app()->getRuntimePath();
            $filename = "exhib_{$model->exhibition_id}.json";
            file_put_contents($path . $filename, json_encode($data));
            $res = (new \AliOss())->uploadFile($path . $filename, "vinehoo/exhib/{$filename}");
        } catch (\Exception $e) {
            Log::write('furshExhib 刷新OSS文件失败3' . $e->getMessage());
        }
    }

    public function furshBonth($model)
    {
        try {
            $brands      = ExhibitBrands::where('booth_id', $model->booth_id)->order('sort', 'DESC')->select()->toArray();
            $brands_data = [];
            foreach ($brands as $brand) {
                $brands_data[] = [
                    'brand'    => $brand,
                    'products' => ExhibitProducts::where('brand_id', $brand['id'])->order('sort', 'DESC')->select()->toArray()];
            }

            $data = [
                'error_code' => 0,
                'error_msg'  => '',
                'data'       => $brands_data
            ];

            $path     = app()->getRuntimePath();
            $filename = "exhib_booth_{$model->booth_id}.json";
            file_put_contents($path . $filename, json_encode($data));
            (new \AliOss())->uploadFile($path . $filename, "vinehoo/exhib/{$filename}");
        } catch (\Exception $e) {
            Log::write('furshBonth3 刷新OSS文件失败' . $e->getMessage());
        }
    }


}