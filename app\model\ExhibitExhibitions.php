<?php

namespace app\model;

use think\facade\Log;

class ExhibitExhibitions extends BaseModel
{


//    #region 展会开始日期 获取器和编辑器
//    public function setStartDateAttr($value)
//    {
//        if (!$value) return null;
//        if (is_numeric($value)) return $value;
//        return $value ? strtotime($value) : null;
//    }
//
//    public function getStartDateAttr($value)
//    {
//        return $value ? date('Y-m-d H:i:s',$value) : null;
//    }
//    #endregion
//
//    #region 展会结束日期 获取器和编辑器
//    public function setEndDateAttr($value)
//    {
//        if (!$value) return null;
//        if (is_numeric($value)) return $value;
//        return $value ? strtotime($value) : null;
//    }
//
//    public function getEndDateAttr($value)
//    {
//        return $value ? date('Y-m-d H:i:s',$value) : null;
//    }
//    #endregion

    #region 更新时间 获取器和编辑器
//    public function setUpdatedAtAttr($value)
//    {
//        if (!$value) return null;
//        if (is_numeric($value)) return $value;
//        return $value ? strtotime($value) : null;
//    }

//    public function getUpdatedAtAttr($value)
//    {
//        return $value ? date('Y-m-d H:i:s',$value) : null;
//    }
    #endregion

    public function getFeaturedProductsButtonImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getTopImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getFeaturedProductsButtonHImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getExhibitingBrandsButtonImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getExhibitingBrandsButtonHImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getFeaturedProductsSectionImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getExhibitingBrandsSectionImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getBottomImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getBrandAboutSectionImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }

    public function getBrandBottomImageUrlAttr($value)
    {
        return $this->imageFullPath($value);
    }


    public static function onAfterWrite($model)
    {
        $model->furshExhib($model);
        (new \CDN())->refresh($model->id);

    }

    public static function onAfterDelete($model)
    {
        $model->furshExhib($model);
        (new \CDN())->refresh($model->id);
    }

    public function furshExhib($model)
    {
        try {
            $exhibition = $model->toArray();
            $featured   = ExhibitProducts::where('is_featured', 1)->where('exhibition_id', $model->id)->order('sort', 'DESC')->select()->toArray();
            $brands     = ExhibitBrands::where('exhibition_id', $model->id)->order('sort', 'DESC')->select()->toArray();

            $data = [
                'error_code' => 0,
                'error_msg'  => '',
                'data'       => compact('exhibition', 'featured', 'brands')
            ];

            $path     = app()->getRuntimePath();
            $filename = "exhib_{$model->id}.json";
            file_put_contents($path . $filename, json_encode($data));
            (new \AliOss())->uploadFile($path . $filename, "vinehoo/exhib/{$filename}");
        } catch (\Exception $e) {
            Log::write('furshExhib 刷新OSS文件失败1' . $e->getMessage());
        }
    }


}