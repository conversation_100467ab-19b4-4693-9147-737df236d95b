<?php

namespace app\model;

use think\facade\Log;

class ExhibitProducts extends BaseModel
{

    protected $append = ['exhibition_name', 'booth_name', 'brand_name'];

    public function getExhibitionNameAttr($value, $row)
    {
        return ExhibitExhibitions::where('id', $row['exhibition_id'])->value('name');
    }

    public function getBoothNameAttr($value, $row)
    {
        return ExhibitBooths::where('id', $row['booth_id'])->value('name');
    }

    public function getBrandNameAttr($value, $row)
    {
        return ExhibitBooths::where('id', $row['brand_id'])->value('name');
    }

    public static function onAfterWrite($model)
    {
        $model->furshExhib($model);
        $model->furshBonth($model);
        (new \CDN())->refresh($model->exhibition_id, $model->booth_id);

    }

    public static function onAfterDelete($model)
    {
        $model->furshExhib($model);
        $model->furshBonth($model);
        (new \CDN())->refresh($model->exhibition_id, $model->booth_id);
    }


    public function furshBonth($model)
    {
        try {
            $brands      = ExhibitBrands::where('booth_id', $model->booth_id)->order('sort', 'DESC')->select()->toArray();
            $brands_data = [];
            foreach ($brands as $brand) {
                $brands_data[] = [
                    'brand'    => $brand,
                    'products' => ExhibitProducts::where('brand_id', $brand['id'])->order('sort', 'DESC')->select()->toArray()];
            }

            $data = [
                'error_code' => 0,
                'error_msg'  => '',
                'data'       => $brands_data
            ];

            $path     = app()->getRuntimePath();
            $filename = "exhib_booth_{$model->booth_id}.json";
            file_put_contents($path . $filename, json_encode($data));
            (new \AliOss())->uploadFile($path . $filename, "vinehoo/exhib/{$filename}");
        } catch (\Exception $e) {
            Log::write('furshBonth4 刷新OSS文件失败' . $e->getMessage());
        }
    }

    public function furshExhib($model)
    {
        try {
            $exhibition = ExhibitExhibitions::where('id', $model->exhibition_id)->find();
            if (!empty($exhibition)) {
                $exhibition = $exhibition->toArray();
            }
            $featured = ExhibitProducts::where('is_featured', 1)->where('exhibition_id', $model->exhibition_id)->order('sort', 'DESC')->select()->toArray();
            $brands   = ExhibitBrands::where('exhibition_id', $model->exhibition_id)->order('sort', 'DESC')->select()->toArray();

            $data = [
                'error_code' => 0,
                'error_msg'  => '',
                'data'       => compact('exhibition', 'featured', 'brands')
            ];

            $path     = app()->getRuntimePath();
            $filename = "exhib_{$model->exhibition_id}.json";
            file_put_contents($path . $filename, json_encode($data));
            $res = (new \AliOss())->uploadFile($path . $filename, "vinehoo/exhib/{$filename}");
        } catch (\Exception $e) {
            Log::write('furshExhib 刷新OSS文件失败4' . $e->getMessage());
        }
    }


}