<?php

namespace app\model;

use think\Model;
use think\model\concern\SoftDelete;

class HoverButton extends Model
{
    use SoftDelete;
    protected $defaultSoftDelete = 0;
    protected $autoWriteTimestamp = true;
    protected $json = ['params'];

    public function getParamsAttr($value,$data)
    {
        $result = (array)$value;
        $result["icon"] = env("ALIURL").$result["icon"];
        return $result;
    }

    #region 上架时间 获取器和编辑器
    public function setStartTimeAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getStartTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : null;
    }
    #endregion
    #region 上架时间 获取器和编辑器
    public function setEndTimeAttr($value)
    {
        if (!$value) return null;
        if (is_numeric($value)) return $value;
        return $value ? strtotime($value) : null;
    }

    public function getEndTimeAttr($value)
    {
        return $value ? date('Y-m-d H:i:s', $value) : null;
    }
    #endregion

}