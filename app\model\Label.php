<?php

namespace app\model;

class Label extends Base
{
	protected $name = 'label';

	// 关联客户端路径
	public function clientPath() {
        return $this->hasOne(ClientPath::class, 'id', 'path_id');
    }

    public function adPathParam() {
        return $this->hasMany(LabelPathExtends::class, 'lid', 'id');
    }


    public function getLaPathValueParamAttr($value,$data) {

        if($data['path_id']) {
            $list = ClientPathExtends::field("id,title,pid")->whereRaw("pid=:pid and (ios_val='' and android_val='' and mini_val='' and h5_val='' )", ['pid' => $data['path_id']])->select()->toArray();

            $emptyvalarr = array_column($list,"id");

            $result = LabelPathExtends::field("peid as id,title,ios_val as value")->whereIn('peid',$emptyvalarr)->where("lid",$data['id'])->select();
            return $result;
        }
        return [];
    }

}