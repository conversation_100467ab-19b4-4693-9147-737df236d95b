<?php

namespace app\service\es;

class Facade
{
    protected $es;
    protected $index;
    protected $query;
    protected $body;
    protected $response;
    protected $type = '_doc';

    public function __construct($client)
    {
        $this->es = $client;
    }

    /**
     * 设置ES索引
     */
    public function setIndex($index)
    {
        $this->index = $index;

        return $this;
    }

    /**
     * 添加数据
     * @param array $param 是一个一维关联数组
     */
    public function insert($param)
    {
        $data   = [
            'index' => $this->index,
            'type'  => $this->type,
            'id'    => $param['id'],
            'body'  => $param,
        ];
        $result = $this->es->create($data);
        return $result;
    }

    /**
     * id删除数据
     * @param array $param 是一个一维关联数组
     */
    public function deleteId($id)
    {
        $data   = [
            'index' => $this->index,
            'type'  => $this->type,
            'id'    => $id,
        ];
        $result = $this->es->delete($data);

        return $result;
    }

    /**
     * 根据id修改数据
     * @param array $param 是一个一维关联数组
     */
    public function update($param)
    {
        $data = [
            'index' => $this->index,
            'type'  => $this->type,
            'id'    => $param['id'],
            'body'  => ['doc' => $param],
        ];

        $result = $this->es->update($data);
        return $result;
    }

    /**
     * 条件删除数据
     * @param array $param 是一个一维关联数组
     */
    public function delete()
    {
        $params = [
            'index' => $this->index,
            'type'  => $this->type,
            'body'  => [
                'query' => $this->query,
            ]
        ];
        $result = $this->es->deleteByQuery($params);

        return $result;
    }

    /**
     * 条件修改数据
     * @param array $param 是一个一维关联数组
     */
    public function save($data)
    {
        $fields = '';
        foreach ($data as $k => $v) {
            if (is_numeric($v)) {
                $fields .= "ctx._source.{$k} = $v;";
            } else {
                $fields .= "ctx._source.{$k} = '$v';";
            }
        }
        $fields = trim($fields, ';');
        $params = [
            'index' => $this->index,
            'type'  => $this->type,
            'body'  => [
                'query'  => $this->query,
                'script' => [
                    "inline" => $fields,
                    'lang'   => 'painless'

                ]
            ]
        ];
        return $this->es->updateByQuery($params);
    }

    /**
     * 自定义搜索条件
     * @param array $query 是一个多维关联数组
     */
    public function whereQuery($query)
    {
        $this->query = $query;

        return $this;
    }

    /**
     * 自定义Bool搜索条件
     * @param array $param 是一个多维关联数组
     */
    public function whereBool($param)
    {
        $query = ['bool' => ['filter' => [], 'must' => []]];

        #查找多个精准值 [['id' => [1,2,3]]]
        if (!empty($param['terms'])) {
            $terms = [];
            foreach ($param['terms'] as $v) {
                array_push($terms, ['terms' => $v]);
            }
            $query['bool']['filter'] = array_merge($query['bool']['filter'], $terms);
        }

        #范围查询 ['created_time'=>['gte'=>2021,'lte'=>2022]]
        if (!empty($param['range'])) {
            $range = [];
            foreach ($param['range'] as $v) {
                array_push($range, ['range' => $v]);
            }
            $query['bool']['filter'] = array_merge($query['bool']['filter'], $range);
        }

        #匹配查询 [['name'=>'张三']]
        if (!empty($param['match'])) {
            $match = [];
            foreach ($param['match'] as $v) {
                array_push($match, ['match' => $v]);
            }
            $query['bool']['must'] = array_merge($query['bool']['must'], $match);
        }

        #模糊查询 [['name'=>'*张三*']]
        if (!empty($param['wildcard'])) {
            $wildcard = [];
            foreach ($param['wildcard'] as $v) {
                array_push($wildcard, ['wildcard' => $v]);
            }
            $query['bool']['must'] = array_merge($query['bool']['must'], $wildcard);
        }

        #多字段搜索
        if (!empty($param['multi_match'])) {
            $multi_match = [];
            foreach ($param['multi_match'] as $v) {
                array_push($multi_match, ['multi_match' => $v]);
            }
            $query['bool']['must'] = array_merge($query['bool']['must'], $multi_match);
        }

        $this->query = $query;

        return $this;
    }

    /**
     * ES搜索条件
     * @param array $param 是一个多维关联数组 [['id','=',1]]
     */
    public function where($param)
    {
        $query = ['bool' => ['filter' => [], 'must' => []]];
        $match = $terms = $range = $wildcard = $matchNot = [];

        foreach ($param as $v) {
            if (isset($v[0]) && isset($v[1]) && isset($v[2])) {
                switch ($v[1]) {
                    #匹配查询 ['name','=','张三']
                    case '=':
                        array_push($match, ['match' => [$v[0] => $v[2]]]);
                        break;
                    #分词强匹配查询 ['name','=','张三']
                    case '==':
                        array_push($match, ['match_phrase' => [$v[0] => $v[2]]]);
                        break;
                    #匹配查询 ['name','<>','张三']
                    case '<>':
                        array_push($matchNot, ['match' => [$v[0] => $v[2]]]);
                        break;
                    #查找多个精准值 ['id','in',[1,2]]
                    case 'in':
                        array_push($terms, ['terms' => [$v[0] => $v[2]]]);
                        break;
                    #查找多个精准值 ['id','not in',[1,2]]
                    case 'not in':
                        array_push($matchNot, ['terms' => [$v[0] => $v[2]]]);
                        break;
                    #范围查询 ['name','>','2']
                    case '>':
                        array_push($range, ['range' => [$v[0] => ['gt' => $v[2]]]]);
                        break;
                    #范围查询 ['name','>=','2']
                    case '>=':
                        array_push($range, ['range' => [$v[0] => ['gte' => $v[2]]]]);
                        break;
                    #范围查询 ['name','<','2']
                    case '<':
                        array_push($range, ['range' => [$v[0] => ['lt' => $v[2]]]]);
                        break;
                    #范围查询 ['name','<=','2']
                    case '<=':
                        array_push($range, ['range' => [$v[0] => ['lte' => $v[2]]]]);
                        break;
                    #范围查询 ['name','range',['gte'=>2020,'lte'=>2021]]
                    case 'range':
                        array_push($range, ['range' => [$v[0] => $v[2]]]);
                        break;
                    #模糊查询 ['name','like','*张三*']
                    case 'like':
                        array_push($wildcard, ['wildcard' => [$v[0] => $v[2]]]);
                        break;
                }
            }
        }

        !empty($terms) && $query['bool']['filter'] = array_merge($query['bool']['filter'], $terms);
        !empty($range) && $query['bool']['filter'] = array_merge($query['bool']['filter'], $range);
        !empty($match) && $query['bool']['must'] = array_merge($query['bool']['must'], $match);
        !empty($wildcard) && $query['bool']['must'] = array_merge($query['bool']['must'], $wildcard);
        !empty($matchNot) && $query['bool']['must_not'] = $matchNot;

        $this->query = $query;

        return $this;
    }

    /**
     * ES or搜索条件
     * @param array $param 是一个多维关联数组 [['id','=',1]]
     */
    public function whereOr($param)
    {
        $query  = $this->query;
        $should = [];

        foreach ($param as $v) {
            if (isset($v[0]) && isset($v[1]) && isset($v[2])) {
                switch ($v[1]) {
                    #匹配查询 ['name','=','张三']
                    case '=':
                        array_push($should, ['match' => [$v[0] => $v[2]]]);
                        break;
                    #分词强匹配查询 ['name','==','张三']
                    case '==':
                        array_push($should, ['match_phrase' => [$v[0] => $v[2]]]);
                        break;
                    #模糊查询 ['name','like','*张三*']
                    case 'like':
                        array_push($should, ['wildcard' => [$v[0] => $v[2]]]);
                        break;
                }
            }
        }

        if (!empty($should)) {
            $query['bool']['must'][] = [
                'bool' => ['should' => $should],
            ];
        }

        $this->query = $query;
        return $this;
    }

    /**
     * 字段筛选
     * @param string $param 是一个字符串:'id,name,...'
     */
    public function field($param)
    {
        $param        = str_replace(array("\r\n", "\r", "\n", " "), "", $param);
        $source       = explode(',', $param);
        $this->source = $source;
        return $this;
    }

    /**
     * 分页
     * @param int $from 分页起始值
     * @param int $size 最大数不能大于1W
     */
    public function limit($from, $size)
    {
        $this->from = $from;
        $this->size = $size;
        return $this;
    }

    /**
     * 排序
     * @param array $param 是一个多维关联数组:['id'=>'desc',...]
     */
    public function order($param)
    {
        $this->sort = $param;
        return $this;
    }

    /**
     * 获取查询条件
     */
    public function getData()
    {
        $body = ['size' => 10000, 'from' => 0];
        #字段筛选
        !empty($this->source) && $body['_source'] = $this->source;
        #查询条件
        !empty($this->query) && $body['query'] = $this->query;
        #最大数不能大于1W
        !empty($this->size) && $body['size'] = $this->size > 10000 ? 10000 : $this->size;
        #分页
        !empty($this->from) && $body['from'] = $this->from;
        #排序
        !empty($this->sort) && $body['sort'] = $this->sort;

        return [
            'index' => $this->index,
            'body'  => $body,
        ];
    }

    /**
     * 获取查询批量返回数据
     */
    public function select()
    {
        $this->response = $this->es->search($this->getData());
        return $this;
    }

    public function toArray()
    {
        $info = $this->get();
        return $info['data'] ?? [];
    }

    /**
     * 获取查询总数
     */
    public function count()
    {
        $this->limit(0, 1);
        $this->response = $this->es->search($this->getData());
        $info           = $this->get();
        return $info['total']['value'] ?? 0;
    }

    /**
     * 获取查询单条返回数据
     */
    public function find()
    {
        $this->limit(0, 1);
        $this->response = $this->es->search($this->getData());
        $info           = $this->get();
        return $info['data'][0] ?? [];
    }

    /**
     * 获取查询单字段
     */
    public function value($fields)
    {
        $this->limit(0, 1);
        $this->field($fields);
        $this->response = $this->es->search($this->getData());
        $info           = $this->get();
        return $info['data'][0][$fields] ?? [];
    }

    /**
     * 获取查询返回数据
     */
    public function get()
    {
        $response = $this->response;
        if (empty($response)) {
            return [];
        }

        if (
            array_key_exists('_shards', $response)
            && !empty($response['_shards']) && is_array($response['_shards'])
            && array_key_exists('failed', $response['_shards']) && $response['_shards']['failed'] == 0
        ) {
            if (array_key_exists('hits', $response)) {
                $data['total'] = $response['hits']['total'];
                $data['data']  = [];
                if (array_key_exists('hits', $response['hits'])) {
                    $hits = $response['hits']['hits'];
                    foreach ($hits as $item) {
                        if (array_key_exists('_source', $item)) {
                            $value = $item['_source'];
                            foreach ($value as $k => $v) {
                                $v === '-' && $value[$k] = '';
                            }
                            array_push($data['data'], $value);
                        }
                    }
                    return $data;
                }
            }
        }
        return [];
    }

    /**
     * 兼容旧文档查询
     * @param array $param 是一个多维关联数组
     */
    public function getDocumentList($param)
    {
        $body  = ['size' => $param['limit'] ?? 10000, 'from' => 0];
        $query = ['bool' => ['filter' => [], 'must' => []]];

        #查找多个精准值 [['id' => [1,2,3]]]
        if (!empty($param['terms'])) {
            $terms = [];
            foreach ($param['terms'] as $v) {
                array_push($terms, ['terms' => $v]);
            }
            $query['bool']['filter'] = array_merge($query['bool']['filter'], $terms);
        }

        #范围查询 ['created_time'=>['gte'=>2021,'lte'=>2022]]
        if (!empty($param['range'])) {
            $range = [];
            foreach ($param['range'] as $v) {
                array_push($range, ['range' => $v]);
            }
            $query['bool']['filter'] = array_merge($query['bool']['filter'], $range);
        }

        #匹配查询 [['name'=>'张三']]
        if (!empty($param['match'])) {
            $match = [];
            foreach ($param['match'] as $v) {
                array_push($match, ['match' => $v]);
            }
            $query['bool']['must'] = array_merge($query['bool']['must'], $match);
        }

        #模糊查询 [['name'=>'*张三*']]
        if (!empty($param['wildcard'])) {
            $wildcard = [];
            foreach ($param['wildcard'] as $v) {
                array_push($wildcard, ['wildcard' => $v]);
            }
            $query['bool']['must'] = array_merge($query['bool']['must'], $wildcard);
        }

        #多字段搜索
        if (!empty($param['multi_match'])) {
            $multi_match = [];
            foreach ($param['multi_match'] as $v) {
                array_push($multi_match, ['multi_match' => $v]);
            }
            $query['bool']['must'] = array_merge($query['bool']['must'], $multi_match);
        }
        $body['query'] = $query;

        #字段
        !empty($param['source']) && $body['_source'] = $param['source'];
        #分页
        if (!empty($param['page'])) {
            $body['from'] = ($param['page'] - 1) * $body['size'];
        }
        #排序
        if (!empty($param['sort'])) {
            $sort = [];
            foreach ($param['sort'] as $value) {
                array_push($sort, $value);
            }
            $body['sort'] = $sort;
        }

        $esPrefix = env('ES.PREFIX');
        foreach ($param['index'] as &$value) {
            $value = $esPrefix . $value;
        }
        $this->response = $this->es->search([
            'index' => $param['index'],
            'body'  => $body,
        ]);

        return $this->get();
    }
}
