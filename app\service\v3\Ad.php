<?php

namespace app\service\v3;

use app\BaseController;
use think\App;
use think\cache\driver\Redis;
use think\facade\Cache;
use think\facade\Log;
use think\Request;

class Ad
{
    public $options = [
        'host'       => '127.0.0.1',
        'port'       => 6379,
        'password'   => '',
        'select'     => 0,
        'timeout'    => 0,
        'expire'     => 0,
        'persistent' => false,
        'prefix'     => '',
        'tag_prefix' => 'tag:',
        'serialize'  => [],
    ];

    public function __construct ()
    {
//        parent::initialize(); // TODO: Change the autogenerated stub
        $this->options["host"]= env("CACHE.HOST","127.0.0.1");
        $this->options["port"]= env("CACHE.PORT","3306");
        $this->options["password"]= env("CACHE.PASSWORD","");
        $this->options["prefix"]= env("CACHE.prefix","vinehoo");
    }

    static public function list($param)
    {

        $where = function($query) use($param) {
            $query->where('type', $param['type']??1);
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', $param['status']);
            }

            if (isset($param['pattern']) && strlen($param['pattern']) > 0) {
                $query->whereFindInSet('pattern', $param['pattern']);
            }

            if (isset($param['title']) && strlen($param['title']) > 0) {
                $query->where('title', 'like', '%' . $param['title'] . '%');
            }

            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', 'like', '%' . $param['channel'] . '%');
            }

            if (isset($param['client']) && strlen($param['client']) > 0) {
                $query->where('client', 'like', '%' . $param['client'] . '%');
            }


            if (isset($param['type']) && $param['type'] == 3 &&  isset($param['form'])&&$param['form'] == "client") {//类型为弹窗时，并且来源为客户端
                if($param["uid"] != null){
                    $paramuser = ['uid'=>$param["uid"]];
                    $newUser = \app\service\v3\Common::chackNewUser($paramuser);
                    $query->where('pattern', $newUser);
                }else{
                    $query->where('pattern', 3);
                }

            }

            //商家查询
            if (isset($param['mid']) && strlen($param['mid']) > 0) {
                $query->where('mid','=',$param['mid']);
            }else{
                $query->where('mid', '=', 0);
            }

        };

        $append = ["operator_name","pattern_name","client_path"];
        $order = ['sort' => 'desc', 'created_at' => 'desc'];

        if($param['type'] == 2){//开屏广告
            array_push($append,"gather_status");//返回新增综合状态
            unset($order['sort']);//删除排序字段
        }
        $list = \app\model\Ad::getInf([
            'with' => [
                'ad_path_param' => function($query) {
                    $query->find();
                }
            ],
            'where' => $where,
            'order' => $order,
            'page' => $param['page'] ?? 1,
            'limit' => $param['limit'] ?? 10,
            'type' => 'select'
        ])->append($append);


        $total = \app\model\Ad::getInf([
            'where' => $where,
            'type' => 'count'
        ]);
        return [
            'list' => $list,
            'total' => $total
        ];
    }

    //创建广告
    static public function create($param){

        $data = $param['params'];
        unset($param['params']);

        #region 弹窗广告,新增和编辑设置状态为禁用. 2022年8月15日16:39:24 @cct
        // type 类型：1-banner，2-开屏，3-弹窗，4-胶囊，5-广告位，6-新banner
        if(!empty($param['type']) && $param['type'] == 3){
            $param['status'] = 0;
        }
        //开屏广告
        if(!empty($param['type']) && $param['type'] == 2){
            $param['status'] = 0;
        }
        #endregion

        $model = new \app\model\Ad();

        $model->save($param);
        $id = $model->id;

        if(!$id){
            excep('添加异常');
        }
        if(isset($param['path'])){
            AdPathExtends::create($id,$data,$param['path']);
        }
//        if(($param['type'] == 2 || $param['type'] == 3 ) && time()<strtotime($param['end_time'])) self::setAdCache($id);


        return true;
    }

    /**
     * 设置缓存
     * @param $id
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    static public function setAdCache($id)
    {
        $ads = new Ad();
        $redis = new Redis($ads->options);
        $resouce = \app\model\Ad::find($id)->append(["pattern_name","client_path","modul_data"])->toArray();//数据
        $rediskey = $resouce['type'] == 2?"vinehoo.ad.openscreen":"vinehoo.ad.popup";
        $num = Cache::set($rediskey,json_encode($resouce));
//        $num = $redis->lpush($rediskey,json_encode($resouce));
        if(!$num) error_log("广告插入redis数据库失败,id:".$id);
    }

    static public function update($param){

        $info = \app\model\Ad::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('数据不存在');
        }

        #region 弹窗广告,新增和编辑设置状态为禁用. 2022年8月15日16:39:24 @cct
        // type 类型 1banner 2 开屏 3 弹窗 4 胶囊 5广告位
        if(!empty($param['type']) && $param['type'] == 3){
            $param['status'] = 0;
        }
        #endregion

        if (!$info->save($param)) {
            excep('编辑异常');
        }
        $data = $param['params'];
        foreach ($data as $k=>$v){
            $data[$k]['peid'] = $v['id'];
            $data[$k]['pid'] = $param['path'];
        }
        if(isset($param['path'])){
            \app\model\AdPathExtends::where("aid",$param['id'])->delete();
            \app\service\v3\AdPathExtends::create($param['id'],$data,$param['path']);
        }
       /*foreach ($data as $k=>$v){
            $data[$k]['ios_val']=$v['value'];
            $data[$k]['android_val']=$v['value'];
            $data[$k]['mini_val']=$v['value'];
            $data[$k]['h5_val']=$v['value'];
            $data[$k]['pc_val']=$v['value'];
            unset($data[$k]['value']);

            $obj = new  \app\model\AdPathExtends();
            $num = $obj->where("id",$v['id'])->update($data[$k]);
//            var_dump($num);
       }*/

       //缓存
//        $ads = new Ad();
//        if(($param['type'] == 2 || $param['type'] == 3 ) && time()<strtotime($param['end_time'])) $ads->cacheCreate();//更新全部缓存
        return true;
    }

    /**
     * @方法描述: 根据商品上下架, 关联广告自动上下架
     * <AUTHOR>
     * @Date 2022/8/16 15:00
     * @param $param
     * @return bool
     */
    static public function syncGoodsStatus($param)
    {
        $pids = [7,56]; // 路径 id = 7 商品详情
        $ad_ids = \app\model\AdPathExtends::where([
//            ['pid', '=', $pid],
            ['pid', 'in', $pids],
            ['ios_val', '=', $param['period']],
        ])->column('aid');
        // $type_ids = [2, 3]; // 弹窗广告和开机广告不处理  type 类型 1banner 2 开屏 3 弹窗 4 胶囊 5广告位

        $status = intval(in_array($param['onsale_status'], [1,2])); //onsale_status 商品的上架状态（0：待上架，1：待售中，2：在售中，3：已下架,  4：已售罄）

        $res = \app\model\Ad::where(function ($query) use ($ad_ids, $param) {
            $query->whereOr('id', 'in', $ad_ids);
            $query->whereOr(function ($query) use ($param) {
                $query->where([
                    ['modul', '=', 4], //模块 1：酒会 2：直播3：专题活动 4:商品（广告位）
                    ['modul_id', '=', $param['period']],
                ]);
            });
        })
            // ->where('type', "not in", $type_ids)
            ->whereBetweenTimeField('start_time', 'end_time')
            ->update(['status' => $status]);

        Log::info('广告关联商品状态上下架 关联商品:' . $param['period'] . ' 状态为:' . $status . ' 影响数据条数:' . $res. PHP_EOL);


//        if($status == 0){
            $res = \app\model\CardGoodsLive::where('relation_id','=',$param['period'])
                ->where('type','=',1)->update(['status' => $status]);

            Log::info('卡片商品状态上下架 关联商品:' . $param['period'] . ' 状态为:' . $status . ' 影响数据条数:' . $res. PHP_EOL);
//        }

        return true;
    }

    /**
     * 全量缓存更新
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function cacheCreate()
    {
        $where = function($query) {
            $query->where('status', 1);
            //上架时间判断
            $nowtime = time();
            $query->where('end_time',">=",$nowtime);
            $query->where('type',"=",2);
        };
        $openscreenData = \app\model\Ad::where($where)->order('sort','desc')->select()->append(['client_path','ad_path_param'])->toArray();
        $openscreenData2 = \app\model\Ad::where($where)->where('id', "NOT IN", [701, 702])->order('sort','desc')->select()->append(['client_path','ad_path_param'])->toArray();
        Cache::set('vinehoo.ad.openscreen',json_encode($openscreenData));
        Cache::set('vinehoo.ad.openscreen20230118',json_encode($openscreenData2));
        $where = function($query) {
            $query->where('status', 1);
            //上架时间判断
            $nowtime = time();
            $query->where('end_time',">=",$nowtime);
            $query->where('type',"=",3);

        };
        $popupData = \app\model\Ad::where($where)->order('sort','desc')->select()->append(['client_path','ad_path_param'])->toArray();
        $popupData2 = \app\model\Ad::where($where)->where('id', "NOT IN", [701, 702])->order('sort','desc')->select()->append(['client_path','ad_path_param'])->toArray();
        Cache::set('vinehoo.ad.popup',json_encode($popupData));
        Cache::set('vinehoo.ad.popup20230118',json_encode($popupData2));

    }


    static public function detail($param){
        $obj = new \app\model\Ad();
        $resoult = $obj->find($param['id'])->append(['ad_path_value_param']);
        return $resoult;
    }

    static function clientlist($param){

        $header_client = ['ios' => 0, 'android' => 1, 'miniapp' => 2, 'h5' => 3, 'vinehoo-pc' => 4,];
        if(!isset($param['client']) || (strlen($param['client']) == 0)){
            $param['client'] = $header_client[\request()->header('vinehoo-client', '')] ?? '';
        }
        ($param['client'] == 5) && $param['client'] = 1;
        $where = function($query) use($param) {
            #region 根据版本号返回内容 @cct
            //允许的查询参数
            $clients  = [0, 1]; //客户端 0:ios 1:安卓 2:小程序 3:h5
            $channels = [0, 1, 2, 3, 4, 5]; // 频道 0 首页 1 闪购 2 秒发3 社区 4 兔头 5个人中心

            //先判断参数是否符合
            if ((!empty($param['type']) && in_array($param['type'], [3, 4])) &&
                !empty($param['vinehoo_client_version']) &&
                isset($param['client']) && in_array($param['client'], $clients) &&
                isset($param['channel']) && in_array($param['channel'], $channels)) {
                //根据客户端判断版本

                // 当前 安卓版本号 9.0.7 IOS版本号 9.13 --2023年1月17日10:09:30
                if (($param['client'] == 0 && $param['vinehoo_client_version'] < 9.14) ||
                    ($param['client'] == 1 && $param['vinehoo_client_version'] < 9.08)) {

                    //老版本过滤掉 ID 701 拍卖 不展示
                    $query->where('id', "NOT IN", [701, 702]);

                }
            }
            #endregion

            $query->where('type', $param['type']??1);
            $query->where('status', 1);


            if (isset($param['pattern']) && strlen($param['pattern']) > 0) {
                $query->where('pattern', $param['pattern']);
            }

            if (isset($param['title']) && strlen($param['title']) > 0) {
                $query->where('title', 'like', '%' . $param['title'] . '%');
            }

            if(isset($param['mid']) && !is_array($param['mid'])&& strlen($param['mid'])){

            }else{
                //查询平台广告
                $query->where('mid', 0);
                if (isset($param['channel']) && strlen($param['channel']) > 0) {
                    $query->where('channel', 'like', '%' . $param['channel'] . '%');
                }
            }


            if (isset($param['client']) && strlen($param['client']) > 0) {
                $query->where('client', 'like', '%' . $param['client'] . '%');
            }

            if (isset($param['type']) && $param['type'] == 3) {//类型为弹窗时，并且来源为客户端
                if($param["uid"] != null){
                    $paramuser = ['uid'=>$param["uid"]];
                    $newUser = \app\service\v3\Common::chackNewUser($paramuser);
                    $query->where('pattern', $newUser);
                }else{
                    $query->where('pattern', 3);
                }

            }

            //上架时间判断
            $nowtime = time();
            $query->where('start_time',"<=",$nowtime);
            $query->where('end_time',">=",$nowtime);

        };
         $wheredata = [
             'with' => [
                 'ad_path_param' => function($query) {
                     $query->find();
                 }
             ],
             'where' => $where,
//             'whereIn' => ['field'=>'mid','vaule'=>$param['mid']],
             'order' => ['sort' => 'desc', 'created_at' => 'desc'],
             'type' => 'select'
         ];
        if(isset($param['mid']) && !is_array($param['mid'])&& strlen($param['mid'])){
            $wheredata['whereIn'] = ['field'=>'mid','vaule'=>$param['mid']];
        }
        //广告位
        if($param['type'] == 5){
            $patharr = [1=>"winePartyDetail",2=>"LiveDetail",3=>"webNoTitle",4=>"GoodsDetail"];
            //判断用户是否白酒禁用
            $restrictstatus = Common::restrictUser(['ids'=>$param["uid"]]);
//            var_dump($restrictstatus);
            if($restrictstatus){

//                unset($wheredata['ad_path_param']);
//                $csd= $wheredata;
                $adspace = \app\model\Ad::where($where)->where('modul',4)->column('modul_id');//->append(["pattern_name","client_path","modul_data"])
//                var_dump($adspace);
                $period = self::getadspaceperiod($adspace);

                if($period!=[]){
//                    var_dump(array_column($period,'id'));exit();
//                    $list = \app\model\Ad::getInf($wheredata)->whereIn('modul_id',array_column($period,'id'))->append(["pattern_name","client_path","modul_data"])->toArray();
                    $list = \app\model\Ad::where($where)->whereNotIn('modul_id',array_column($period,'id'))->append(["pattern_name","client_path","modul_data"])->select()->toArray();

//                    var_dump($list);
                    $patharr = [1=>"winePartyDetail",2=>"LiveDetail",3=>"webNoTitle",4=>"GoodsDetail"];
                    foreach ($list as $k=>$v){
                        $pathdata = self::getADSpacecPathParam($patharr[$v['modul']],$v['modul_id'],$param['vine_client'] ?? '');
                        $list[$k]['client_path'] = $pathdata['client_path'];
                        $list[$k]['ad_path_param'] = $pathdata['client_path_param'];
                    }
//                    echo json_encode($list);
                    $total = \app\model\Ad::whereNotIn('modul_id',array_column($period,'id'))->where($where)->count();

                    return [
                        'list' => $list,
                        'total' => $total
                    ];
                }
            }
        }

        $list = \app\model\Ad::getInf($wheredata)->append(["pattern_name","client_path","modul_data"])->toArray();

//        echo \app\model\Ad::getLastsql();
        if($param['type'] == 5){//广告位 参数单独处理
            $patharr = [1=>"winePartyDetail",2=>"LiveDetail",3=>"webNoTitle",4=>"GoodsDetail"];
            //判断用户是否白酒禁用
//            $code = Common::restrictUser(['ids'=>$param["uid"]]);
//            var_dump($code);
            foreach ($list as $k=>$v){
                //判断白酒
                $pathdata = self::getADSpacecPathParam($patharr[$v['modul']],$v['modul_id'],$param['vine_client'] ?? '');
                $list[$k]['client_path'] = $pathdata['client_path'];
                $list[$k]['ad_path_param'] = $pathdata['client_path_param'];
                if ($v['modul'] == 2 && isset($param['vine_client']) && in_array($param['vine_client'],['miniapp','ios','android']) && $list[$k]['modul_data'] === null) {
                    $list[$k]['modul_data'] = [
                        'start_time' => date('Y-m-d') . ' 19:00-22:00',
                        'num' => 5500,
                    ];
                }
            }


        }


        if($param['type'] == 1){//banner 限制尺寸

            if (isset($param['client']) && strlen($param['client']) > 0 &&$param['client']==4){//客户端pc

            }else{
                foreach ($list as $k=>$v){

                    $sjf = strpos($list[$k]['image'],'?')?"&":"?";
                    $extstr = strstr($list[$k]['image'],"?",true)?:$list[$k]['image'];
                    $w =631; $h=309;$ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                    $list[$k]['image'] = $list[$k]['image'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                }
            }

        }

        if($param['type'] == 4){//胶囊 限制尺寸
            if (isset($param['client']) && strlen($param['client']) > 0 &&$param['client']==4){//客户端pc

            }else{
                foreach ($list as $k=>$v){
                    $sjf = strpos($list[$k]['image'],'?')?"&":"?";
                    $extstr = strstr($list[$k]['image'],"?",true)?:$list[$k]['image'];
                    $w =877; $h=257;$ext = strrev(strchr(strrev($extstr),'.',true));//后缀
                    $list[$k]['image'] = $list[$k]['image'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext;
                }
            }

        }

        //曝光度
        if($param['type'] == 1 || $param['type'] == 5){
            $periods = [];
            foreach ($list as $v){
                $modul = $v['modul'];
                $period = $v['modul_id'];
                if($modul == 4){
                    array_push($periods,$period);
                }
            }
            //曝光度
            adExposurelog(['periods'=>$periods]);
        }
        $total = \app\model\Ad::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        //只返回一个商家的广告
        if(isset($param['mid']) && !is_array($param['mid'])&& strlen($param['mid'])>0){

            $listarr = [];
            foreach ($list as $v){
                $listarr[$v['mid']][] = $v;
            }
            $midarr = explode(',',$param['mid']);
            foreach ($midarr as $v){

                if(isset($listarr[$v]) && count($listarr[$v])>0){
                    $list = $listarr[$v];
                    $total = count($list);
                    break;
                }
            }
        }
        return [
            'list' => $list,
            'total' => $total
        ];
    }


    public static function getADSpacecPathParam($onlyCode,$id,$client = '')
    {
        if($onlyCode == 'LiveDetail'){
            //小程序使用MiniLiveRoom
            if($client == 'miniapp'){
                $result = \app\model\ClientPath::where("code",'MiniLiveRoom')->find()->append(['client_path_param']);
            }elseif ($client == 'ios' || $client == 'android'){
                $result = \app\model\ClientPath::where("code",'WxXCX')->find()->append(['client_path_param']);
            }else{
                $result = \app\model\ClientPath::where("code",$onlyCode)->find()->append(['client_path_param']);
            }
        }else{
            $result = \app\model\ClientPath::where("code",$onlyCode)->find()->append(['client_path_param']);
        }
        if(empty($result)){//空
            return ['client_path_param'=>[],'client_path'=>[]];
        }
        //数据

        $resuktarray = $result['client_path_param'];
        if($onlyCode == 'webNoTitle'){//活动
            $mothod = env('ITEM.ACTIVITIES_MANAGEMENT_URL').'/activity/v3/activity/detail?id='.$id;
            $urlresult = get_url($mothod);
            $activity_url = '';
            $urlresult =json_decode($urlresult,true);
            if(isset($urlresult) && @$urlresult['error_code'] == 0){//获取活动链接
                $activity_url = $urlresult['data']['activity_url'];
            }
            foreach ($resuktarray as $k=>$v){//给参数上链接
                if($v['title'] == 'url'){
                    $resuktarray[$k]['ios_val'] =$activity_url;
                    $resuktarray[$k]['android_val'] =$activity_url;
                    $resuktarray[$k]['mini_val'] =$activity_url;
                    $resuktarray[$k]['h5_val'] =$activity_url;
                }
            }
        }

        if($onlyCode == 'GoodsDetail' ||$onlyCode == 'winePartyDetail' || $onlyCode == 'LiveDetail'  ){//商品 和酒会
            foreach ($resuktarray as $k=>$v){//给参数上链接
                if($v['title'] == 'id'){
                    $resuktarray[$k]['ios_val'] =$id;
                    $resuktarray[$k]['android_val'] =$id;
                    $resuktarray[$k]['mini_val'] =$id;
                    $resuktarray[$k]['h5_val'] =$id;
                }
            }
        }

        if($onlyCode == 'LiveDetail') {
            //小程序使用MiniLiveRoom
            if ($client == 'miniapp') {
                $resuktarray = [];

                $result['mini_path'] .= '?room_id='.$id.'&custom_params=%7B%22one_source_platform%22%3A%22vinehoo%22%2C%22one_source_event%22%3A%22Livestream-4%22%7D';
                $result['client_path_param'] = [];
            }elseif ($client == 'ios' || $client == 'android'){
                $result['client_path_param'][0]['ios_val'] = 'gh_e64b2bfc0bc5';
                $result['client_path_param'][0]['android_val'] = 'gh_e64b2bfc0bc5';
                $result['client_path_param'][1]['ios_val']= '/pages/index/index?room_id='.$id.'&custom_params=%7B%22one_source_platform%22%3A%22vinehoo%22%2C%22one_source_event%22%3A%22Livestream-4%22%7D';
                $result['client_path_param'][1]['android_val']= '/pages/index/index?room_id='.$id.'&custom_params=%7B%22one_source_platform%22%3A%22vinehoo%22%2C%22one_source_event%22%3A%22Livestream-4%22%7D';
            }
        }
        $redata = ['client_path_param'=>$resuktarray,'client_path'=>$result];
        return $redata;
    }

    /**
     * 获取期数
     * @param array $ids
     * @return array
     */
    static public function getadspaceperiod(array $ids){

        $periodsWhere['bool']['must'][] = ['term'=>['is_user_filter'=>1]];//需要过滤的商品
        $periodsWhere['bool']['must'][] = ['terms'=>['id'=>$ids]];
        $field = 'id';
        $params = [
            'index' => 'vinehoo.periods',
            'type' => '_doc',
            'body' => [
                '_source'=>explode(',',$field),
                'query' => $periodsWhere,
                'track_total_hits'=>true
            ],

        ];
//        echo json_encode($params);
        $resoucedata = esindex($params);

        return $resoucedata;
    }

    public static function prescription($param)
    {


        $user_id = ['uid'=>$param['uid']];
        $newUser = \app\service\v3\Common::chackNewUser($user_id);
        //老用户 未登录 无法查看
        if($newUser == 2 || $newUser == 3){
            return [
                'list' => [],
                'total' => 0
            ];
        }

        $where = function($query) use($param) {
            $query->where('type', '=',$param['type']??7);
            $query->where('status', 1);


            if (isset($param['pattern']) && strlen($param['pattern']) > 0) {
                $query->where('pattern', $param['pattern']);
            }

            if (isset($param['title']) && strlen($param['title']) > 0) {
                $query->where('title', 'like', '%' . $param['title'] . '%');
            }

            if(isset($param['mid']) && !is_array($param['mid'])&& strlen($param['mid'])){

            }else{
                //查询平台广告
                $query->where('mid', 0);
                if (isset($param['channel']) && strlen($param['channel']) > 0) {
                    $query->where('channel', 'like', '%' . $param['channel'] . '%');
                }
            }


            if (isset($param['client']) && strlen($param['client']) > 0) {
                $query->where('client', 'like', '%' . $param['client'] . '%');
            }

            if (isset($param['type']) && $param['type'] == 3) {//类型为弹窗时，并且来源为客户端
                if($param["uid"] != null){
                    $paramuser = ['uid'=>$param["uid"]];
                    $newUser = \app\service\v3\Common::chackNewUser($paramuser);
                    $query->where('pattern', $newUser);
                }else{
                    $query->where('pattern', 3);
                }

            }

            //上架时间判断
            $nowtime = time();
            $query->where('start_time',"<=",$nowtime);
            $query->where('end_time',">=",$nowtime);

        };
        $wheredata = [
            'with' => [
                'ad_path_param' => function($query) {
                    $query->find();
                }
            ],
            'where' => $where,
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
            'type' => 'select'
        ];
        if(isset($param['mid']) && !is_array($param['mid'])&& strlen($param['mid'])){
            $wheredata['whereIn'] = ['field'=>'mid','vaule'=>$param['mid']];
        }

        $list = \app\model\Ad::getInf($wheredata)->append(["pattern_name","client_path","modul_data"])->toArray();
//        echo \app\model\Ad::getLastsql();
        $total = \app\model\Ad::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        //只返回一个商家的广告
        if(isset($param['mid']) && !is_array($param['mid'])&& strlen($param['mid'])>0){

            $listarr = [];
            foreach ($list as $v){
                $listarr[$v['mid']][] = $v;
            }
            $midarr = explode(',',$param['mid']);
            foreach ($midarr as $v){

                if(isset($listarr[$v]) && count($listarr[$v])>0){
                    $list = $listarr[$v];
                    $total = count($list);
                    break;
                }
            }
        }
        return [
            'list' => $list,
            'total' => $total
        ];

    }

    /**
     * 定时任务 修改广告状态
     * @return void
     */
    public static function taskstatusSave()
    {
        $where = [
            ['end_time',"<",time()],
            ['status',"=",1]
        ];
        $result = \app\model\Ad::where($where)->select()->toArray();
        if(!$result) return true;

        \app\model\Ad::whereIn('id',array_column($result,'id'))->save(['status'=>0]);

        return true;
    }
}