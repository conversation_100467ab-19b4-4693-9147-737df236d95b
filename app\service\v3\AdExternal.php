<?php

namespace app\service\v3;

use app\service\elasticsearch\ElasticSearchService;
use think\Exception;
use think\facade\Log;

class AdExternal
{

    static public function getWineParty($wpid)
    {
        $url = '/wineparty/v3/wineparty/detail';
        $urlcode = httpCurl(env('ITEM.WINEPARTY_URL').$url."?party_id=".$wpid);

        if($urlcode){//正确调用
            $response = json_decode($urlcode, true);

            if (!isset($response['error_code']) || $response['error_code'] != 0) {
                Log::info("数据：酒会调用失败，party_id:".$wpid);
                excep("酒会调用失败：".$response['error_msg']);
            }
            if(empty($response['data'])){//返回值为空
                Log::info("数据：酒会查询数据为空，party_id:".$wpid);
                return null;
            }
            return $response['data'];
        }else{
            Log::info("接口：酒会调用失败，party_id:".$wpid);
            excep("酒会调用失败,请联系管理员.");
            return null;
        }
    }

    static public function getLiveDetail($liveid)
    {
        $url = '/live/v3/live/detail';
        $urlcode = httpCurl(env('ITEM.LIVE_URL').$url."?live_id=".$liveid);
        if($urlcode){
            $response = json_decode($urlcode, true);
            if (!isset($response['error_code']) || $response['error_code'] != 0) {
                Log::info("直播调用失败，".$response['error_msg'].$liveid);
                return null;
//                excep("直播调用失败，".$response['error_msg']);
            }
            return $response['data'];
        }else{
            return null;
        }
    }

    static public function getActivityDetail($aid)
    {
        $url = '/activity/v3/activity/detail';
        $urlcode = httpCurl(env('ITEM.ACTIVITIES_MANAGEMENT_URL').$url."?id=".$aid);
        if($urlcode){
            $response = json_decode($urlcode, true);
            if (!isset($response['error_code']) || $response['error_code'] != 0) {
                Log::info("活动专题调用失败，".$response['error_msg'].$aid);
                return null;
            }
            return $response['data'];
        }else{
            return null;
        }
    }

    /**
     * 获取商品详情
     * @param $pid
     * @return mixed|null
     */
    static public function getGoodDetail($pid)
    {
        $url = '/commodities/v3/periods/getESPeriodInfoById';
        $urlcode = httpCurl(env('ITEM.COMMODITIES_URL').$url."?period=".$pid);
        if($urlcode){
            $response = json_decode($urlcode, true);
            if (!isset($response['error_code']) || $response['error_code'] != 0) {
                Log::info("商品调用失败，".$response['error_msg'].$pid);
                return null;
            }
            return $response['data'];
        }else{
            return null;
        }
    }

    /**
     * 批量获取商品
     * @param string $ids
     * @return mixed|null
     */
    static public function getGoodListByids(string $ids, $therms = []){

        try {
            if($ids == "") return ['list'=>[]];
            $periods = explode(",", $ids);
//            echo $ids.PHP_EOL;
//            var_dump($periods).PHP_EOL;
            $therms[] = ['id' => $periods];
            $es = new ElasticSearchService();
            $params = [
                'index' => ['periods'],
                'match' => [],
                'terms' => $therms,
//            'source' => ['id','title','brief','product_img','onsale_status'],
//            'range' => $range,
//            'page' => $from,
//            'limit' => $size,
            'limit' => count($periods),
//            'sort' => $sort,
            ];

//            echo json_encode($params);
            $data = $es->getDocumentList($params);
            if(isset($data['data']) && count($data['data'])>0){
                return ['list'=>$data['data']];
            }else{
                return ['list'=>[]];
            }
        }catch (Exception $e){
            echo $e->getMessage();
            exit();
        }catch (\Elasticsearch\Common\Exceptions\BadRequest400Exception $e){
            echo $e->getMessage().PHP_EOL;
            return ['list'=>[]];
        }

    }

    static public function getLiveListByids(string $ids){

        $url = '/live/v3/live/list';
        $urlcode = httpCurl(env('ITEM.LIVE_URL').$url."?live_ids=".$ids);
        if($urlcode){
            $response = json_decode($urlcode, true);
            if (!isset($response['error_code']) || $response['error_code'] != 0) {
                Log::info("直播调用失败，".$response['error_msg'].$ids);
                return null;
//                excep("直播调用失败，".$response['error_msg']);
            }
            return $response['data'];
        }else{
            return null;
        }
    }

}