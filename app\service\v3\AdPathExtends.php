<?php

namespace app\service\v3;

use think\Log;

class AdPathExtends
{

    /**
     * @param $aid 广告ID
     * @param $param 参数 [{peid:1,value:1,pid:1},{peid:2,vaule:1,pid:1}]
     * @param $pid 路径ID
     * @throws \app\CustomException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    static public function create($aid,$param,$pid){

        $code = \app\model\ClientPathExtends::where('pid',$pid)->select()->toArray();

        if($code){
            $countnum = count($code);
            foreach ($code as $k=>$v){//获取插入数据

                $data = $param;
                $code[$k]['aid'] = $aid;

                if($data){
                    $pidsArr = array_column($param,"peid");//获取皮带数组
                    $pidsParamArrs = array_column($data,null,"peid");//获取皮带数组
                    if(in_array($v['id'],$pidsArr)){
                        $code[$k]['ios_val']=$pidsParamArrs[$v['id']]['value'];
                        $code[$k]['android_val']=$pidsParamArrs[$v['id']]['value'];
                        $code[$k]['mini_val']=$pidsParamArrs[$v['id']]['value'];
                        $code[$k]['h5_val']=$pidsParamArrs[$v['id']]['value'];
                        $code[$k]['pc_val']=$pidsParamArrs[$v['id']]['value'];
                    }
                }
                $code[$k]['peid'] = $v['id'];
                unset($code[$k]['id']);
            }

//            echo json_encode($code);
            $resultcount = \app\model\AdPathExtends::insertAll($code);
            if($countnum !== $resultcount){
                \think\facade\Log::info("广告添加成功，路径配置添加异常.id:".$pid);
                excep('广告添加成功，路径配置添加异常');
            }
        }

    }
}