<?php

namespace app\service\v3;

use app\model\Bullein as BulleinModel;
use think\facade\Db;

class Bullein
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', $param['status']);
            }
        };
        // 列表
        $list = BulleinModel::getInf([
            'where' => $where,
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);


        // 总条数
    	$total = BulleinModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {
        /*$param += [
            'created_at' => time()
        ];*/

        $BulleinModel = new BulleinModel();
        if (!$BulleinModel->save($param)) {
            excep('添加异常');
        }
        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $info = BulleinModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('快报不存在','info',20117);
        }

        if (!$info->save($param)) {
            excep('编辑异常');
        }
        return true;
    }

    static function detail($param) {
        $info = BulleinModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'type' => 'find'
        ]);

        $info = BulleinModel::where("id",$param['id'])->find();

        if (!$info)  excep('快报不存在','info',20117);

        return $info->toArray();
    }

    /**
     * 删除
     * @return bool
     */
    static function delete($param) {
        $info = BulleinModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('快报不存在','info',20117);
        }

        if (!$info->delete()) {
            excep('删除异常');
        }
        return true;
    }

    /**
     * 选中
     * @return bool
     */
    static function checked($param) {
        // 启动事务
        Db::startTrans();
        try {
            // 删除之前得选中状态
            $old_checked = BulleinModel::getInf([
                'where' => function($query) {
                    $query->where('status', 1);
                },
                'field' => 'id',
                'type' => 'find'
            ]);
            if ($old_checked) {
                $old_checked->save(['status' => 0]);
            }

            // 添加选中状态
            $checked = BulleinModel::getInf([
                'where' => function($query) use($param) {
                    $query->where('id', $param['id']);
                },
                'field' => 'id',
                'type' => 'find'
            ]);
            if ($checked) {
                $checked->save(['status' => 1]);
            }
            // 提交事务
            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            Log::write($e->getMessage());
            excep('更新异常');
            return false;
        }

        $BulleinModel = new BulleinModel();
        if (!$BulleinModel->save($param)) {
            excep('添加异常', 'error');
        }
        return true;
    }

    /**
     * 获取选中快报
     * @return array
     */
    static function getChecked($param) {
        // 列表
        $info = BulleinModel::getInf([
            'where' => function($query) {
                $query->where('status', 1);
            },
        	'field' => 'id, url, context',
            'type' => 'find'
    	]);
        if (!$info) {
            excep('快报不存在','info',20117);
        }
        
        return $info;
    }

    static function clientlist($param) {
        $where = function($query) use($param) {
            $nowtime = time();

            $query->where('status', 1);
            $query->where('start_time',"<=",$nowtime);
            $query->where('end_time',">=",$nowtime);
        };
        // 列表
        $list = BulleinModel::getInf([
            'where' => $where,
            'field'=>"id,title",
            'page' => $param['page'] ?? 1,
            'limit' => $param['limit'] ?? 10,
            'type' => 'select'
        ]);


        // 总条数
        $total = BulleinModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
            'list' => $list,
            'total' => $total
        ];
    }


    static function clientdetail($param) {
       /* $info = BulleinModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'type' => 'find'
        ]);*/

        $info = BulleinModel::field("id,title,context")->where("id",$param['id'])->find();

        if (!$info)  excep('快报不存在','info',20117);

        return $info->toArray();
    }


}
