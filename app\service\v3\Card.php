<?php

namespace app\service\v3;

use app\model\Card as CardModel;
use app\model\CardGoodsLive;
use think\facade\Cache;
use think\facade\Db;

class Card
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', $param['channel']);
            }
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', $param['status']);
            }
            if (isset($param['pattern']) && strlen($param['pattern']) > 0) {
                $query->where('pattern', $param['pattern']);
            }
            if (isset($param['card_name']) && strlen($param['card_name']) > 0) {
                $query->where('card_name','like', "%".$param['card_name']."%");
            }
            if (isset($param['mid']) && strlen($param['mid']) > 0) {
                $query->where('mid', $param['mid']);
            }else{
                $query->where('mid', 0);
            }
        };

        // 列表
        $list = CardModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);//->append(["card_extend_detail"])

        if (!empty($list)) {
            $list = $list->toArray();
            $card_ids = array_column($list, 'id');
            // 商品数量
            $goods_nums = Db::name('card_goods_live')
                ->whereIn('cid', $card_ids)
                ->group('cid')
                ->column('cid,count(1) as c', 'cid');

            // 获取筛选项
            $card_filter = Db::name('card_filter')
                ->where('is_delete', 0)
                ->whereIn('card_id', $card_ids)
                ->order('sort asc,id desc')
                ->column('id,card_id,name,sort,add_method,auto_add_type,auto_add_content');
            $card_filter_info = [];
            foreach ($card_filter as $v) {
                $v['auto_add_content'] = !empty($v['auto_add_content']) ? json_decode($v['auto_add_content'], true) : [];
                $card_filter_info[$v['card_id']][] = $v;
            }

            foreach ($list as &$v) {
                $v['card_data_nums'] = $goods_nums[$v['id']] ?? 0;
                $v['filter'] = $card_filter_info[$v['id']] ?? [];
            }
        }

        // 总条数
    	$total = CardModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    static function detail($param)
    {
        $where = function($query) use($param) {
            $query->where('id', $param['id']);
        };
        // 信息
        $resoult = CardModel::getInf([
            'where' => $where,
            'type' => 'find'
    	]);

        if (!empty($resoult)) {
            // 商品数量
            $goods_nums = Db::name('card_goods_live')
                ->where('cid', $resoult['id'])
                ->count();

            // 获取筛选项
            $card_filter = Db::name('card_filter')
                ->where('is_delete', 0)
                ->where('card_id', $resoult['id'])
                ->order('sort asc,id desc')
                ->column('id,card_id,name,sort,add_method,auto_add_type,auto_add_content');
            foreach ($card_filter as &$v) {
                $v['auto_add_content'] = !empty($v['auto_add_content']) ? json_decode($v['auto_add_content'], true) : [];
            }

            $resoult['card_data_nums'] = $goods_nums;
            $resoult['filter']         = $card_filter ?? [];
        }
        return $resoult;
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {
        $time = time();

        $param += [
            'created_at' => $time
        ];
        Db::startTrans();
        try {
            $filter = [];
            if (!empty($param['filter'])) {
                $filter = $param['filter'];
                unset($param['filter']);
            }

            $CardModel = new CardModel();
            $CardModel->save($param);
            // 获取自增ID
            $id = $CardModel->id;

            // 自动添加商品
            $add_method = 0;
            if (!empty($param['add_method']) && $param['add_method'] == 1) {
                $add_method = 1;
            }

            //筛选项
            if (isset($filter)) {
                $card_filter = $filterids = [];
                foreach ($filter as $key => $value) {
                    // 自动添加商品
                    if (!empty($value['add_method']) && $value['add_method'] == 1) {
                        $add_method = 1;
                        if (empty($value['auto_add_type']) || !is_numeric($value['auto_add_type'])) {
                            excep('请选择自动添加类型');
                        }
                        if (empty($value['auto_add_content']) || !is_array($value['auto_add_content'])) {
                            excep('请选择定义内容');
                        }
                        foreach ($value['auto_add_content'] as $v2) {
                            if (!isset($v2['id']) || !isset($v2['name'])) {
                                excep('定义内容格式错误');
                            }
                        }
                    }
                    $value['auto_add_content'] = !empty($value['auto_add_content']) ? json_encode($value['auto_add_content']) : '';

                    $filter_info = [
                        'name'             => $value['name'],
                        'card_id'          => $id,
                        'sort'             => $key,
                        'created_at'       => $time,
                        'update_at'        => $time,
                        'add_method'       => $value['add_method'] ?? 0,
                        'auto_add_type'    => $value['auto_add_type'] ?? 0,
                        'auto_add_content' => $value['auto_add_content'] ?? '',
                    ];
                    if (!empty($value['id'])) {
                        $filterids[] = $value['id'];
                        Db::name('card_filter')->where('id', $value['id'])
                            ->update($filter_info);
                    } else {
                        $card_filter[] = $filter_info;
                    }
                }

                if (!empty($card_filter)) {
                    Db::name('card_filter')->insertAll($card_filter);
                }
            }

            if ($add_method == 1) {
                // 把所有的在售商品按照规则自动添加商品到卡片
                //AutomaticallyAddAllPeriod([], [$id]);
            }

            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }

        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $time = time();
        $info = CardModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id,add_method,page_mode',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('卡片不存在');
        }
        if (isset($param['page_mode']) && $info['page_mode'] != $param['page_mode']) {
            excep('编辑时不能修改模式');
        }

        Db::startTrans();
        try {
            $filter = [];
            if (!empty($param['filter'])) {
                $filter = $param['filter'];
                unset($param['filter']);
            }
            $info->save($param);
            
            // 自动添加商品
            $add_method = 0;
            if (!empty($param['add_method']) && $param['add_method'] == 1 && empty($info['add_method'])) {
                $add_method = 1;
            }

            // 查询筛选项
            $fids = Db::name('card_filter')->where('card_id', $param['id'])->column('id');
            $card_filter = $filterids = [];
            foreach ($filter as $key => $value) {
                // 自动添加商品
                if (!empty($value['add_method']) && $value['add_method'] == 1) {
                    if (empty($value['auto_add_type']) || !is_numeric($value['auto_add_type'])) {
                        excep('请选择自动添加类型');
                    }
                    if (empty($value['auto_add_content']) || !is_array($value['auto_add_content'])) {
                        excep('请选择定义内容');
                    }
                    foreach ($value['auto_add_content'] as $v2) {
                        if (!isset($v2['id']) || !isset($v2['name'])) {
                            excep('定义内容格式错误');
                        }
                    }
                }
                $value['auto_add_content'] = !empty($value['auto_add_content']) ? json_encode($value['auto_add_content']) : '';

                $filter_info = [
                    'name'             => $value['name'],
                    'card_id'          => $param['id'],
                    'sort'             => $key,
                    'update_at'        => $time,
                    'add_method'       => $value['add_method'] ?? 0,
                    'auto_add_type'    => $value['auto_add_type'] ?? 0,
                    'auto_add_content' => $value['auto_add_content'] ?? '',
                ];
                if (!empty($value['id'])) {
                    $filterids[] = $value['id'];
                    Db::name('card_filter')->where('id', $value['id'])
                        ->update($filter_info);
                } else {
                    if ($filter_info['add_method'] == 1) {
                        $add_method = 1;
                    }
                    $filter_info['created_at'] = $time;
                    $card_filter[] = $filter_info;
                }
            }
            if (!empty($card_filter)) {
                Db::name('card_filter')->insertAll($card_filter);
            }
            // 删除旧筛选项
            $del_ids = [];
            foreach ($fids as $v) {
                if (!in_array($v, $filterids)) {
                    $del_ids[] = $v;
                }
            }
            if (!empty($del_ids)) {
                Db::name('card_filter')->where('id', 'in', $del_ids)->update(['is_delete' => 1]);
                Db::name('card_goods_filter')->where('filter_id', 'in', $del_ids)->delete();
            }

            if ($add_method == 1) {
                // 把所有的在售商品按照规则自动添加商品到卡片
                //AutomaticallyAddAllPeriod([], [$param['id']]);
            }

            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }

        return true;
    }

    static function clientlist($param,$mid = 0){

        $where = function($query) use($param,$mid) {
            $query->where('status', 1);
            $query->where('mid', $mid);
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', $param['channel']);
            }
        };

        // 列表
        $list = CardModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
            'type' => 'select'
        ])->toArray();

        $list = CardModel::getCardExtendDetailBatch($list);

        // 总条数
        $total = CardModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);


        return [
            'list' => $list,
            'total' => $total
        ];
    }

    static function delect($param){

        Db::startTrans();
        try {
            CardModel::where('id',$param['id'])->delete();
            CardGoodsLive::where('cid',$param['id'])->delete();
            // 删除筛选项相关数据
            Db::name('card_filter')->where('card_id', $param['id'])->delete();
            Db::name('card_goods_filter')->where('card_id', $param['id'])->delete();
            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }
    }
    /**
     * 设置缓存
     * @return void
     */
    static function setCardCache(){
        $flashparam = ['channel'=>2];//秒发
        $indexparam = ['channel'=>0];//首页


        //首页缓存
        $iresult = self::clientlist($indexparam);
//        echo json_encode($iresult);exit();
        $cacheresult = Cache::set('card_index',json_encode($iresult));
//        echo Cache::get('card_index');

        //秒发缓存
        $fresult = self::clientlist($flashparam);
//        echo json_encode($fresult);exit();
        $cacheresult = Cache::set('card_flash',json_encode($fresult));
//        var_dump($cacheresult);

        //商家缓存
        self::setMerchantsCache();
    }

    static function getCardCache($channel){
        if($channel == 0) return Cache::get('card_index');
        if($channel == 2) return Cache::get('card_flash');

    }

    static function setMerchantsCache(){
        $midarr = self::getMerchantsmid();
        $redis = Cache::handler();

        foreach ($midarr as $v){

            $result = self::clientlist(['channel'=>2],$v['id']);
            $rediskeys ='vinehoo.card.merchant.flash';
            $redis->hset($rediskeys,$v['id'],json_encode($result));

            $result = self::clientlist(['channel'=>0],$v['id']);
            $rediskeys = 'vinehoo.card.merchant.index';
            $redis->hset($rediskeys,$v['id'],json_encode($result));
        }

    }

    static function getMerchantsmid(){
        $domain = env('ITEM.VMALL_URL').'/vmall/v3/merchant/list?shop_status=1&limit=100';
        $data = get_url($domain);
        $result = json_decode($data,true);
        if(!$result) excep('获取商家失败');
        $result = $result['data']['list'];
        return $result;
    }

    /**
     * 卡片子项列表
     * @return array
     */
    static function cardfilterlist($param)
    {
        $where = function ($query) use ($param) {
            $query->where('is_delete', 0);
            $query->where('card_id', $param['cid']);
        };

        // 列表
        $list = Db::name('card_filter')->where($where)
            ->order('sort asc,id desc')
            ->page($param['page'] ?? 1, $param['limit'] ?? 10)
            ->select();

        // 总条数
        $total = Db::name('card_filter')->where($where)->count();

        return [
            'list'  => $list,
            'total' => $total
        ];
    }

    /**
     * 添加卡片子项
     * @return bool
     */
    static function cardfilteradd($param)
    {
        $time  = time();
        $param += [
            'created_at' => $time,
            'update_at'  => $time,
        ];

        Db::startTrans();
        try {
            if (Db::name('card_filter')->where([['name', '=', $param['name']], ['card_id', '=', $param['card_id']]])->value('id')) {
                excep('子项名称已存在');
            }

            $CardModel = new CardModel();
            $card_info = $CardModel->where('id', $param['card_id'])->find();
            if ($card_info['page_mode'] == 0) {
                excep('页面模式为浏览不支持添加子项');
            }

            // 自动添加商品验证
            if (!empty($param['add_method']) && $param['add_method'] == 1) {
                if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                    excep('请选择自动添加类型');
                }
                if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                    excep('请选择定义内容');
                }
                foreach ($param['auto_add_content'] as $v) {
                    if (!isset($v['id']) || !isset($v['name'])) {
                        excep('定义内容格式错误');
                    }
                }
                $param['auto_add_content'] = json_encode($param['auto_add_content']);
            } else {
                $param['auto_add_content'] = '';
            }

            if (!Db::name('card_filter')->insert($param)) {
                excep('添加异常');
            }

            // 自动添加商品
            if (!empty($param['add_method']) && $param['add_method'] == 1) {
                // 把所有的在售商品按照规则自动添加商品到卡片
                //AutomaticallyAddAllPeriod([], [$param['card_id']]);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 编辑卡片子项
     * @return bool
     */
    static function cardfilteredit($param)
    {
        $time  = time();
        $param += [
            'update_at' => $time,
        ];

        Db::startTrans();
        try {
            if (Db::name('card_filter')->where([['id', '<>', $param['id']], ['name', '=', $param['name']], ['card_id', '=', $param['card_id']]])->value('id')) {
                excep('子项名称已存在');
            }
            $card_filter = Db::name('card_filter')->where('id', $param['id'])->find();
            if (empty($card_filter)) {
                excep('子项不存在');
            }

            $CardModel = new CardModel();
            $card_info = $CardModel->where('id', $param['card_id'])->find();
            if ($card_info['page_mode'] == 0) {
                excep('页面模式为浏览不支持编辑子项');
            }

            // 自动添加商品验证
            if (!empty($param['add_method']) && $param['add_method'] == 1) {
                if (empty($param['auto_add_type']) || !is_numeric($param['auto_add_type'])) {
                    excep('请选择自动添加类型');
                }
                if (empty($param['auto_add_content']) || !is_array($param['auto_add_content'])) {
                    excep('请选择定义内容');
                }
                foreach ($param['auto_add_content'] as $v) {
                    if (!isset($v['id']) || !isset($v['name'])) {
                        excep('定义内容格式错误');
                    }
                }
                $param['auto_add_content'] = json_encode($param['auto_add_content']);
            } else {
                $param['auto_add_content'] = '';
            }

            if (!Db::name('card_filter')->where('id', $param['id'])->update($param)) {
                excep('编辑异常');
            }

            // 自动添加商品
            if (!empty($param['add_method']) && $param['add_method'] == 1 && $card_filter['add_method'] == 0) {
                // 把所有的在售商品按照规则自动添加商品到卡片
                //AutomaticallyAddAllPeriod([], [$param['card_id']]);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 删除卡片子项
     * @return bool
     */
    static function cardfilterdel($param)
    {
        Db::startTrans();
        try {
            $card_filter = Db::name('card_filter')->where('id', $param['id'])->find();
            if (empty($card_filter)) {
                excep('子项不存在');
            }

            Db::name('card_filter')->where('id', $param['id'])->update(['is_delete' => 1]);
            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 编辑状态
     * @return bool
     */
    static function updateStatus($param)
    {
        // vh_card表中没有updated_time、updateor_id、updateor字段，只保留需要更新的字段
        $updateData = [
            'status' => $param['status'],
        ];
        Db::startTrans();
        try {
            $CardModel = new CardModel();
            $info      = $CardModel::getInf([
                'where' => function ($query) use ($param) {
                    $query->where('id', $param['id']);
                },
                'field' => 'id,add_method,page_mode',
                'type'  => 'find'
            ]);
            if (!$info) {
                excep('卡片不存在');
            }

            if (!$info->save($updateData)) {
                excep('编辑异常');
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }

        return true;
    }

    /**
     * 前端商品列表接口（根据page_mode返回不同格式）
     * @param array $param
     * @return array
     */
    static function clientgoodslist($param)
    {
        // 兼容原有接口：支持id和cid两种参数名
        $card_id = $param['id'] ?? $param['cid'] ?? 0;
        if (!$card_id) {
            excep('卡片ID不能为空');
        }

        // 获取卡片信息
        $card_info = CardModel::getInf([
            'where' => function($query) use($card_id) {
                $query->where('id', $card_id);
                $query->where('status', 1);
            },
            'field' => 'id,card_name,page_mode,style,goods_sort_type,main_title,secondary_title,share_image,share_url',
            'type' => 'find'
        ]);

        if (!$card_info) {
            excep('卡片不存在');
        }

        // 根据page_mode调用不同的方法
        if ($card_info['page_mode'] == 0) {
            // 浏览模式：调用类似原有cardgoodslive/clientgoodslist的逻辑
            return self::browseModeGoodsList($param, $card_info);
        } else {
            // 筛选模式：调用类似cardgoodslive/filtergoodslist的逻辑
            return self::filterModeGoodsList($param, $card_info);
        }
    }

    /**
     * 浏览模式商品列表（类似column/clientgoodslist）
     * @param array $param
     * @param array $card_info
     * @return array
     */
    private static function browseModeGoodsList($param, $card_info)
    {
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        $fid = $param['fid'] ?? 0;  // 筛选项ID，兼容原有接口

        // 获取卡片商品
        $card_goods = Db::name('card_goods_live')
            ->where('cid', $param['id'])
            ->where('status', 1)
            ->order('sort desc')
            ->column('id,relation_id,title,sub_title,sort,created_at,type,channel', 'relation_id');

        $array_ids = array_column($card_goods, 'relation_id');
        $from = 0;
        $size = 10000;
        $sort = [['sort' => 'desc']];

        // 指定字段排序
        if (!empty($param['sort_type']) && !empty($param['order'])) {
            $from = ($page - 1) * $limit;
            $size = $limit;
            if ($param['sort_type'] == 'purchased') {
                $sort = [
                    [
                        '_script' => [
                            'type' => 'number',
                            'script' => [
                                'lang' => 'painless',
                                'source' => "doc['purchased'].value + doc['vest_purchased'].value"
                            ],
                            'order' => $param['order']
                        ]
                    ]
                ];
            } else {
                $sort = [[$param['sort_type'] => $param['order']]];
            }
        }

        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];
        $es = new \app\service\elasticsearch\ElasticSearchService();
        $params = [
            'index' => ['periods'],
            'terms' => $therms,
            'page' => $from,
            'limit' => $size,
            'sort' => $sort,
        ];

        // 查询es商品信息
        $data = $es->getDocumentList($params);
        $goods_map = $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                // 优惠标签
                $v['discount_label'] = [];
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_map[$v['id']] = $v;
                $goods_list[] = $v;
            }
        }

        // 如果指定了筛选项ID，需要获取筛选项关联信息
        $goods_filter_map = [];
        if ($fid > 0) {
            $goods_ids = array_column($card_goods, 'id');
            $goods_filter = Db::name('card_goods_filter')
                ->whereIn('goods_id', $goods_ids)
                ->where('filter_id', $fid)
                ->column('goods_id');
            $goods_filter_map = array_flip($goods_filter);
        }

        $filtered_goods_list = [];
        foreach ($goods_list as $g) {
            $c_goods = $card_goods[$g['id']] ?? [];
            if (!empty($c_goods)) {
                // 如果指定了筛选项ID，检查商品是否属于该筛选项
                if ($fid > 0 && !isset($goods_filter_map[$c_goods['id']])) {
                    continue; // 跳过不属于指定筛选项的商品
                }

                if (!empty($c_goods['title'])) {
                    $g['title'] = $c_goods['title'];
                }
                if (!empty($c_goods['sub_title'])) {
                    $g['sub_title'] = $c_goods['sub_title'];
                }
                $g['sort_val'] = $c_goods['sort'];
                $g['created_at'] = $c_goods['created_at'];

                $filtered_goods_list[] = $g;
            }
        }
        $goods_list = $filtered_goods_list;

        // 默认排序
        if ((empty($param['sort_type']) || $param['sort_type'] == 'sort') && !empty($goods_list)) {
            // 排序处理
            $goods_list = self::sortProcessing($goods_list, $card_info);
            // 分页
            $goods_list = array_slice($goods_list, ($page - 1) * $limit, $limit);
        }

        // 计算总数（筛选前的总数）
        $total = count($goods_list);

        // 浏览模式返回原有格式以保持向后兼容性
        return [
            'list' => $goods_list,
            'total' => $total,
            'title' => $card_info['card_name'] ?? '',
            'card_info' => [
                'main_title' => empty($card_info['main_title']) ? (empty($card_info['card_name']) ? "" : $card_info['card_name']) : $card_info['main_title'],
                'secondary_title' => $card_info['secondary_title'] ?? '',
                'share_image' => $card_info['share_image'] ?? '',
                'share_url' => $card_info['share_url'] ?? '',
            ]
        ];
    }

    /**
     * 筛选模式商品列表（类似cardgoodslive/filtergoodslist）
     * @param array $param
     * @param array $card_info
     * @return array
     */
    private static function filterModeGoodsList($param, $card_info)
    {
        // 获取卡片商品
        $card_goods = Db::name('card_goods_live')
            ->where('cid', $param['id'])
            ->where('status', 1)
            ->order('sort desc')
            ->column('id,relation_id,title,sub_title,sort,created_at,type,channel', 'relation_id');

        $array_ids = array_column($card_goods, 'relation_id');
        $sort = [['sort' => 'desc']];

        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];
        $es = new \app\service\elasticsearch\ElasticSearchService();
        $params = [
            'index' => ['periods'],
            'terms' => $therms,
            'limit' => 10000,
            'sort' => $sort,
        ];

        // 查询es商品信息
        $data = $es->getDocumentList($params);
        $goods_map = $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                // 优惠标签
                $v['discount_label'] = [];
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_map[$v['id']] = $v;
                $goods_list[] = $v;
            }
        }

        // 商品筛选项
        $goods_ids = array_column($card_goods, 'id');
        $goods_filter = Db::name('card_goods_filter')
            ->whereIn('goods_id', $goods_ids)
            ->column('goods_id,filter_id');
        $filter_id = [];
        foreach ($goods_filter as $v) {
            $filter_id[$v['goods_id']][] = $v['filter_id'];
        }

        foreach ($goods_list as &$g) {
            $c_goods = $card_goods[$g['id']] ?? [];
            if (!empty($c_goods)) {
                $g['card_filter_id'] = $filter_id[$c_goods['id']] ?? [];

                if (!empty($c_goods['title'])) {
                    $g['title'] = $c_goods['title'];
                }
                if (!empty($c_goods['sub_title'])) {
                    $g['sub_title'] = $c_goods['sub_title'];
                }
                $g['sort_val'] = $c_goods['sort'];
                $g['created_at'] = $c_goods['created_at'];
            }
        }

        // 排序处理
        $goods_list = self::sortProcessing($goods_list, $card_info);
        $card_goods_grouped = [];
        $total = 0;
        foreach ($goods_list as $g) {
            if (empty($g['card_filter_id'])) {
                continue;
            }
            foreach ($g['card_filter_id'] as $fid) {
                $total++;
                $card_goods_grouped[$fid][] = $g;
            }
        }

        // 筛选项
        $card_filter = Db::name('card_filter')
            ->where('is_delete', 0)
            ->where('card_id', $param['id'])
            ->order('sort asc')
            ->column('id,name');
        $list = [];
        foreach ($card_filter as $k => $v) {
            if (empty($card_goods_grouped[$v['id']])) {
                continue;
            }
            $v['goods'] = $card_goods_grouped[$v['id']];
            $list[] = $v;
        }

        // 筛选模式返回与原有filtergoodslist相同的格式以保持向后兼容性
        return [
            'list' => $list,
            'total' => $total,
            'title' => $card_info['card_name'] ?? '',
            'card_info' => [
                'main_title' => empty($card_info['main_title']) ? (empty($card_info['card_name']) ? "" : $card_info['card_name']) : $card_info['main_title'],
                'secondary_title' => $card_info['secondary_title'] ?? '',
                'share_image' => $card_info['share_image'] ?? '',
                'share_url' => $card_info['share_url'] ?? '',
            ]
        ];
    }

    /**
     * 商品排序处理（类似Column的sortProcessing）
     * @param array $goods_list
     * @param array $card_info
     * @return array
     */
    private static function sortProcessing($goods_list, $card_info)
    {
        $sort_group = [];
        foreach ($goods_list as $v) {
            $sort_group[$v['sort_val']][] = $v;
        }
        krsort($sort_group); // 按sort_val降序排列
        $goods_list_data = [];
        foreach ($sort_group as $v) {
            // 根据卡片信息中的排序规则类型进行不同的排序处理
            // 根据数据库字段注释：0=添加时间倒序,1=上架时间倒序,2=随机
            switch ($card_info["goods_sort_type"]) {
                case 0: // 添加时间倒序
                    usort($v, function($a, $b) {
                        $timeA = strtotime($a["created_at"]);
                        $timeB = strtotime($b["created_at"]);
                        if ($timeA == $timeB) {
                            return 0;
                        }
                        return ($timeA < $timeB) ? 1 : -1; // 倒序
                    });
                    break;
                case 1: // 上架时间倒序
                    usort($v, function($a, $b) {
                        $timeA = strtotime($a["onsale_time"]);
                        $timeB = strtotime($b["onsale_time"]);
                        if ($timeA == $timeB) {
                            return 0;
                        }
                        return ($timeA < $timeB) ? 1 : -1; // 倒序
                    });
                    break;
                case 2: // 随机
                    shuffle($v);
                    break;
                default:
                    // 默认不额外排序，保持原有顺序
                    break;
            }
            $goods_list_data = array_merge($goods_list_data, $v);
        }
        return $goods_list_data;
    }

    /**
     * 商品列表（类似Column的goodslist）
     * @param array $param
     * @return array
     */
    static function goodslist($param) {
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        $sort_type = $param['sort_type'] ?? 'sort';
        $order = $param['order'] ?? 'desc';

        $id = $param['activity_label_list'][0] ?? 0;
        $id_list = $param['activity_list'][0] ?? '';
        $id_list = $id_list == '48' ? '-1' : $id_list;

        if ($id_list == '-1') {
            $cid = Db::name('card')
                ->where([
                    'channel' => 20,
                    'status' => 1,
                ])
                ->column('id');
        } else {
            $cid = !empty($id_list) ? explode(',', $id_list) : [];
        }

        $info = [];
        if (!empty($id)) {
            $where = function($query) use($id) {
                $query->where('status', 1);
                $query->where('id', $id);
            };
            // 列表
            $info = CardModel::getInf([
                'where' => $where,
                'type' => 'find',
                'field' => 'id,goods_sort_type'
            ]);
            $cid = [$id];
        }

        //获取商品
        $card_goods = Db::name('card_goods_live')
            ->alias('cgl')
            ->leftJoin('card c','c.id=cgl.cid')
            ->whereIn('cgl.cid', $cid)
            ->where('cgl.status', 1)
            ->order('cgl.sort desc')
            ->column('cgl.id,cgl.cid,cgl.relation_id,cgl.title,cgl.sub_title,cgl.sort,cgl.created_at,c.card_name');
        $period_card = [];
        foreach ($card_goods as $v) {
            $period_card[$v['relation_id']][] = $v;
        }

        $array_ids = array_values(array_unique(array_column($card_goods, 'relation_id')));

        // 分页
        if (!empty($info)) {
            $from = 0;
            $size = 10000;
        } else {
            $from = $page;
            $size = $limit;
        }

        // 排序
        if ($sort_type == 'purchased') {
            $sort = [
                [
                    '_script' => [
                        'type' => 'number',
                        'script' => [
                            'lang' => 'painless',
                            'source' => "doc['purchased'].value + doc['vest_purchased'].value"
                        ],
                        'order' => $order
                    ]
                ]
            ];
        } else {
            $sort = [[$sort_type => $order]];
        }
        $match = [];
        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];

        // 筛选
        if (!empty($param['filters'])) {
            foreach ($param['filters'] as $k => $v) {
                if ($k == 'product_category' && in_array('白酒', $v)) {
                    array_push($v,
                        '酱香型',
                        '浓香型',
                        '清香型',
                        '米香型',
                        '凤香型',
                        '其他香型');
                }
                if (in_array($k, ['regions', 'current_winery', 'current_winery_map', 'label','winery'])) {
                    $k .= '.keyword';
                }
                $therms[] = [$k => $v];
            }
        }
        $range = [];
        if (!empty($param['price_gte']) && !empty($param['price_lte'])) {
            $range[] = [
                'price' => [
                    'gte' => floatval($param['price_gte']),
                    'lte' => floatval($param['price_lte']),
                ]
            ];
        }
        $es = new \app\service\elasticsearch\ElasticSearchService();
        // 查询es商品信息
        $data = $es->getDocumentList([
            'index' => ['periods'],
            'terms' => $therms,
            'range' => $range,
            'match' => $match,
            'page' => $from,
            'limit' => $size,
            'sort' => $sort,
        ]);
        $total = $data['total']['value'] ?? 0;
        $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                $s_card_goods = $period_card[$v['id']] ?? [];
                $card_id = [];
                foreach ($s_card_goods as $val) {
                    $card_id[] = $val['cid'];
                    if (!empty($val['title'])) {
                        $v['title'] = $val['title'];
                    }
                    if (!empty($val['sub_title'])) {
                        $v['sub_title'] = $val['sub_title'];
                    }
                    $v['sort_val'] = $val['sort'];
                    $v['created_at'] = $val['created_at'];
                }
                // 优惠标签
                $v['discount_label'] = [];
                $v['card_id'] = $card_id;
                $v['activity_id'] = $card_id[0] ?? 0;
                $v['activity_label_id'] = $card_id[0] ?? 0;
                // 卡片名称
                $v['special_activity_data']['activity_name'] = $s_card_goods[0]['card_name'] ?? '';
                $v['special_activity_data']['activity_label_name'] = $s_card_goods[0]['card_name'] ?? '';
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_list[] = $v;
            }
        }

        // 默认排序
        if (!empty($goods_list) && !empty($info) && $sort_type == 'sort') {
            //排序处理
            $goods_list = self::sortProcessing($goods_list, $info);
            // 分页
            $goods_list = array_slice($goods_list, ($page - 1) * $limit, $limit);
        }

        return ['list' => $goods_list,'total' => $total > 0 ? $total : 0];
    }

}
