<?php

namespace app\service\v3;

use app\model\Card as CardModel;
use app\model\CardGoodsLive as CardGoodsLiveModel;
use Exception;
use think\facade\Cache;
use think\facade\Log;
use think\facade\Db;

class CardGoodsLive
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        if (!in_array($param['type'], [1, 2])) {
            excep('请输入正确的type值');
        }

        $where = function($query) use($param) {
            // 筛选项过滤
            if (!empty($param['filter_id']) && !is_array($param['filter_id'])) {
                $param['filter_id'] = explode(',', $param['filter_id']);
            }
            // 添加类型过滤
            if (!empty($param['add_type']) && is_array($param['add_type'])) {
                $query->whereIn('add_type', $param['add_type']);
            }

            $query->where('type', $param['type']);
            if (isset($param['cid']) && strlen($param['cid']) > 0) {
                $query->where('cid', $param['cid']);
            }

            // 筛选项过滤逻辑（参照ColumnGoods）
            if (!empty($param['filter_id']) && is_array($param['filter_id'])) {
                if (in_array('-1', $param['filter_id'])) {//查询无子项
                    $query->where(function ($query) use ($param) {
                        $query->whereOr('id', 'NOT IN', function ($query) use ($param) {
                            $query->name('card_goods_filter')
                                ->where('card_id', $param['cid'])
                                ->field('goods_id');
                        });
                        $query->whereOr('id', 'IN', function ($query) use ($param) {
                            $filter_id = array_diff($param['filter_id'], ['-1']);
                            $query->name('card_goods_filter')
                                ->whereIn('filter_id', $filter_id)
                                ->field('goods_id');
                        });
                    });
                } else {//查询子项
                    $query->whereIn('id', function ($query) use ($param) {
                        $query->name('card_goods_filter')
                            ->whereIn('filter_id', $param['filter_id'])
                            ->field('goods_id');
                    });
                }
            }
        };

        $order = ['sort' => 'desc', 'created_at' => 'desc'];
        if (!empty($param['sort'])) {
            $order = ['sort' => $param['sort']];
        }

        // 列表
        $list = CardGoodsLiveModel::getInf([
            'where' => $where,
            'order' => $order,
            'page' => 1,
        	'limit' => 10000,
            'type' => 'select'
    	]);

        $list = $list->toArray();

        if(!$list)return ['list' => [], 'total' => 0];

        $ids = array_column($list, 'id');
        //筛选项
        $card_goods_filter = Db::name('card_goods_filter')
            ->alias('cgf')
            ->leftJoin('card_filter cf','cf.id=cgf.filter_id')
            ->whereIn('cgf.goods_id', $ids)
            ->where('cf.is_delete', 0)  // 排除已删除的筛选项
            ->column('cgf.goods_id,cgf.filter_id,cf.name');
        $filter = $filter_ids = [];
        foreach ($card_goods_filter as $v) {
            $filter[$v['goods_id']][] = $v;
            $filter_ids[$v['goods_id']][] = $v['filter_id'];
        }

        $card = CardModel::find($param['cid']);//卡片
//        echo json_encode($card);
        $array_ids = array_column($list,'relation_id');
        $str_ids = implode(',',$array_ids);

        // ES查询条件 - 支持onsale_status筛选
        $therms = [];
        if (!empty($param['onsale_status']) && is_array($param['onsale_status'])) {
            $therms[] = ['onsale_status' => $param['onsale_status']];
        }

        if($param['type'] == 1) $resouce = AdExternal::getGoodListByids($str_ids, $therms);
        if($param['type'] == 2) $resouce = AdExternal::getLiveListByids($str_ids, $therms);

        $newresouce = $resouce['list'];
        $code = array_column($newresouce,null,'id');


        foreach($list as $key => $val) {
//            $list[$key]['title'] = '';
            $list[$key]['filter'] = $filter[$val['id']] ?? [];
            $list[$key]['filter_id'] = $filter_ids[$val['id']] ?? [];
            $list[$key]['status'] = 0;
            $list[$key]['start_time'] = '';
            switch ($val['type']) {
                case 1://商品
                    if (isset($code[$val['relation_id']])) {
//                        $list[$key]['title'] = $code[$val['relation_id']]['title'];
                        $list[$key]['status'] = $code[$val['relation_id']]['onsale_status'];
                        if($card['pattern'] == 0){
                            $img = explode(',',@$code[$val['relation_id']]['product_img']);
                            $list[$key]['image'] = @$img[0]?env("ALIURL").$img[0]:"";
                        }
                    }
                break;
                case 2://直播
                    if (isset($code[$val['relation_id']])) {
//                        $list[$key]['title'] = $code[$val['relation_id']]['title'];
                        $list[$key]['status'] = $code[$val['relation_id']]['status'];
                        $list[$key]['start_time'] = $code[$val['relation_id']]['start_time'];
                    }
                break;
            }
        }

        // 过滤掉ES中不存在的商品
        $filtered_list = [];
        foreach($list as $val) {
            if (isset($code[$val['relation_id']])) {
                $filtered_list[] = $val;
            }
        }

        // 分页处理
        $total = count($filtered_list);
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        $paginated_list = array_slice($filtered_list, ($page - 1) * $limit, $limit);

        return [
        	'list' => $paginated_list,
        	'total' => $total
        ];
    }

    static function clientgoodslist($param) {
        if (!in_array($param['type'], [1, 2])) {
            excep('请输入正确的type值');
        }

        // 列表
        $list = CardGoodsLiveModel::getInf([
            'where' => function($query) use($param) {
                $query->where('type', $param['type']);
                $query->where('status', 1);
                if (isset($param['cid']) && strlen($param['cid']) > 0) {
                    $query->where('cid', $param['cid']);
                }
            },
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
//            'page' => $param['page'] ?? 1,
//            'limit' => $param['limit'] ?? 10,
            'type' => 'select'
        ]);
        $list = $list->toArray();

        $ids = array_column($list, 'id');
        //筛选项 - 每个商品只匹配一条筛选项（按sort排序取第一条）
        $card_goods_filter = Db::name('card_goods_filter')
            ->alias('cgf')
            ->leftJoin('card_filter cf','cf.id = cgf.filter_id')
            ->whereIn('cgf.goods_id', $ids)
            ->where('cf.is_delete', 0)  // 排除已删除的筛选项
            ->order('cf.sort asc, cf.id asc')  // 按筛选项排序值排序
            ->column('cgf.goods_id,cf.id,cf.name');

        $filter_id = [];
        foreach ($card_goods_filter as $k => $v) {
            // 每个商品只保留第一条筛选项记录
            if (!isset($filter_id[$v['goods_id']])) {
                $filter_id[$v['goods_id']] = [$v];  // 只保存一条记录
            }
        }


        $array_ids = array_column($list,'relation_id');
        $str_ids = implode(',',$array_ids);


        if($param['type'] == 1) $resouce = AdExternal::getGoodListByids($str_ids);
        if($param['type'] == 2) $resouce = AdExternal::getLiveListByids($str_ids);
//        if($param['cid'] == 22) count($resouce['list']);
        $newresouce = $resouce['list'];
        $code = array_column($newresouce,null,'id');


        $rows = [];
        foreach($list as $key => $val) {
            $s_filter = $filter_id[$val['id']] ?? [];

            $list[$key]['title'] = '';
            $list[$key]['status'] = 0;
            $list[$key]['start_time'] = '';
            switch ($val['type']) {
                case 1:
                    $restresult = @$code[$val['relation_id']];

                    if($restresult){
                        $restresult['discount_label'] = [];
                        $restresult['card_filter'] = $s_filter;
                        $restresult['title'] = $code[$val['relation_id']]['title'];
                        $restresult['status'] = $code[$val['relation_id']]['onsale_status'];
                        $restresult['product_img'] = env("ALIURL").$code[$val['relation_id']]['product_img'];
                        $restresult['banner_img'] = env("ALIURL").$code[$val['relation_id']]['banner_img'];
                        if($restresult['onsale_status'] == 2 || $restresult['onsale_status'] == 1)array_push($rows,$restresult);
                    }else{
                        unset($list[$key]);
                    }

                    break;
                case 2:
                    $restresult = @$code[$val['relation_id']];
                    $restresult['title'] = $val['title'];
                    $restresult['card_filter'] = $s_filter;
                    if($restresult) array_push($rows,$restresult);
                    break;
            }
        }

        // 总条数
        $total = CardGoodsLiveModel::getInf([
            'where' => function($query) use($param) {
                $query->where('type', $param['type']);
                $query->where('status', 1);
                if (isset($param['cid']) && strlen($param['cid']) > 0) {
                    $query->where('cid', $param['cid']);
                }
            },
            'type' => 'count'
        ]);

        return [
            'list' => $rows,
            'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {

        $time = time();
        $param += [
            'created_at' => $time,
            'add_type' => $param['add_type'] ?? 0  // 默认为手动添加
        ];
        Db::startTrans();
        try {
            if (!empty($param['filter_id'])) {
                $filter_id = $param['filter_id'];
                unset($param['filter_id']);
            }
            $CardGoodsLiveModel = new CardGoodsLiveModel();
            $CardGoodsLiveModel->save($param);
            // 获取自增ID
            $id = $CardGoodsLiveModel->id;

            // 筛选项
            if (!empty($filter_id)) {
                $card_goods_filter = [];
                foreach ($filter_id as $value) {
                    $card_goods_filter[] = [
                        'goods_id' => $id,
                        'card_id' => $param['cid'],
                        'filter_id' => $value,
                        'created_at'  => $time,
                    ];
                }
                Db::name('card_goods_filter')->insertAll($card_goods_filter);
            }
            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 删除
     * @return bool
     */
    static function delete($param) {
        $info = CardGoodsLiveModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('记录不存在');
        }

        if (!$info->delete()) {
            excep('删除异常');
        }
        return true;
    }

    /**
     * 修改
     * @param $param
     * @return CardGoodsLiveModel
     */
    static function update($param) {
        Db::startTrans();
        try {
            $info = CardGoodsLiveModel::where('id', $param['id'])->find();
            if (!$info) {
                excep('记录不存在');
            }
            if (isset($param['filter_id'])) {
                $filter_id = $param['filter_id'];
                unset($param['filter_id']);
                // 删除筛选项
                Db::name('card_goods_filter')->where('goods_id', $info['id'])->delete();
            }
            
//        $info = CardGoodsLiveModel::where('id',$param['id'])->update(['sort'=>$param['sort']]);
//        var_dump($param);
//        $info = CardGoodsLiveModel::where('id',$param['id'])->allowField(['cid', 'relation_id', 'channel', 'title', 'sub_title','type', 'sort', 'image'])->save($param);
// 筛选项
            if (!empty($filter_id)) {
                $card_goods_filter = [];
                foreach ($filter_id as $value) {
                    $card_goods_filter[] = [
                        'goods_id' => $info['id'],
                        'card_id' => $info['cid'],
                        'filter_id' => $value,
                        'created_at'  => time(),
                    ];
                }
                Db::name('card_goods_filter')->insertAll($card_goods_filter);
            }
            $info = CardGoodsLiveModel::where('id',$param['id'])->update($param);
            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }
        return $info;
    }

    /**
     * 设置卡片商品缓存
     * @return void
     */
    static function setCardGoodCache(){
        //获取所有卡片（有效的）
        $result = CardModel::where("status",1)->order(['sort' => 'desc', 'created_at' => 'desc'])->select();
//        echo json_encode($result);exit();
        //循环获取所有卡片商品（有效的）
        foreach ($result as $v){

            $type = $v['style'] == 7?2:1;
            $param = ['cid'=>$v['id'],'type'=>$type];//参数
            $row = self::clientgoodslist($param);

//            if($v['id'] == 22) echo json_encode($row);

            $cid = $v['id'];//卡片idclear
            if(isset($row['list'])){//数据存在
                // 筛选项
                $card_filter = Db::name('card_filter')
                    ->where('card_id', $cid)
                    ->order('sort asc')
                    ->column('id,name');
                // 卡片信息
                $card_info = CardModel::where('id', $cid)->find();
                $list_periods = array_column($row['list'], 'id');
                // 卡片商品排序
                $goods_sort = CardGoodsLiveModel::where('relation_id', 'in', $list_periods)
                    ->where('cid', $cid)
                    ->order(['sort' => 'desc', 'created_at' => 'desc',])
                    ->column('sort', 'relation_id');
                    
                $countnum = count($row['list']);//数量
                $rowdata  = $row['list'];//数据
                $data = [
                    'list' => $rowdata,
                    'total' => $countnum,
                    'title' => $v['card_name'],
                    'card_filter' => $card_filter,
                    'card_info' => $card_info,
                    'goods_sort' => $goods_sort
                ];//组装好数据
                if($v['id'] == 22) var_dump($countnum);
                $cacheresult = Cache::set('card_good_'.$cid,json_encode($data));//tag(['card_good'])->
            }else{
                $cacheresult = Cache::set('card_good_'.$cid,json_encode([]));
            }

        }

        //设置缓存
    }

    static function getfilter($param) {
        return Db::name('card_filter')
            ->where('card_id', $param['id'])
            ->order('sort asc,id desc')
            ->column('id,name');
    }
}
