<?php

namespace app\service\v3;

use app\model\ClientPath as ClientPathModel;

class ClientPath
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if(isset($param['path_name']) && strlen($param['path_name']) > 0) {
	            $query->where('path_name', 'like', '%'. $param['path_name'] . '%');
	        }
        };

        // 列表
        $list = ClientPathModel::getInf([
            'with' => [
                'client_path_param' => function($query) {
                    $query->find();
                }
            ],
            'where' => $where,
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);


        // 总条数
    	$total = ClientPathModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {

        $ClientPathModel = new ClientPathModel();
        $id = $ClientPathModel->strict(false)->insertGetId($param);
        if (!$id) {
            excep('添加异常');
        }
        //创建配置参数
        ClientPathExtends::create($id,$param['params']);
        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $info = ClientPathModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('快报不存在');
        }

        if (!$info->save($param)) {
            excep('编辑异常');
        }
        return true;
    }

    static public function codefilterlist($param){
        $list = \app\model\ClientPath::where("code",$param)->find();
        if($list) $list = $list->append(["client_path_param"]);
        return $list;
    }

    static public function codefiltermorelist($param){
        $list = \app\model\ClientPath::whereIn("code",$param)->select();
        if($list) $list = $list->append(["client_path_param"]);
        $count = \app\model\ClientPath::whereIn("code",$param)->count();
        $request = ["list"=>$list,"total"=>$count];
        return $request;
    }

    static public function multiplebyids($param){
        $list = \app\model\ClientPath::field('id,path_name')->whereIn("id",$param)->select();
        $count = \app\model\ClientPath::whereIn("id",$param)->count();
        $request = ["list"=>$list,"total"=>$count];
        return $request;
    }
}
