<?php

namespace app\service\v3;

use think\Log;
use app\model\ClientPathExtends as pathExtendsMol;
class ClientPathExtends
{

    static public function create($pid,$param){

        $countnum = count($param);
        foreach ($param as $k=>$v){
            $param[$k]['pid'] = $pid;
        }
        $resultcount = pathExtendsMol::insertAll($param);
        if($countnum !== $resultcount){
            \think\facade\Log::info("路径添加成功，路径配置添加异常.id:".$pid);
            excep('路径添加成功，路径配置添加异常');
        }
    }

    static public function filterlist($param){
        $list = pathExtendsMol::field("id,title,pid")->whereRaw("pid=:pid and (ios_val='' and android_val='' and mini_val='' and h5_val='' )", ['pid' => $param['pid']])->select()->append(["value"]);
        return $list;
    }


}