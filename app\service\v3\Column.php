<?php

namespace app\service\v3;

use app\model\Column as ColumnModel;
use app\model\ColumnGoods;
use app\model\ColumnFilter;
use think\facade\Db;

class Column
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', $param['channel']);
            }
            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', $param['status']);
            }
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', $param['name']);
            }
        };

        // 列表
        $list = ColumnModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'id' => 'desc'],
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);
        

        if($list) $list= $list->append(['goods_nums']);

        if (!empty($list)) {
            $list = $list->toArray();
            $card_ids = array_column($list, 'id');
            // 商品数量
            $goods_nums = Db::name('column_goods')
                ->whereIn('cid', $card_ids)
                ->group('cid')
                ->column('cid,count(1) as c', 'cid');

            // 获取筛选项
            $card_filter = Db::name('column_filter')
                ->where('is_delete', 0)
                ->whereIn('card_id', $card_ids)
                ->order('sort asc,id desc')
                ->column('id,card_id,name,sort,add_method,auto_add_type,auto_add_content');
            $card_filter_info = [];
            foreach ($card_filter as $v) {
                $v['auto_add_content'] = !empty($v['auto_add_content']) ? json_decode($v['auto_add_content'], true) : [];
                $card_filter_info[$v['card_id']][] = $v;
            }

            foreach ($list as &$v) {
                $v['card_data_nums'] = $goods_nums[$v['id']] ?? 0;
                $v['filter'] = $card_filter_info[$v['id']] ?? [];
            }
        }

        // 总条数
    	$total = ColumnModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    static function detail($param)
    {
        $where = function($query) use($param) {
            $query->where('id', $param['id']);
        };
        // 信息
        $resoult = ColumnModel::getInf([
            'where' => $where,
            'type' => 'find'
    	]);

        if (!empty($resoult)) {
            // 商品数量
            $goods_nums = Db::name('column_goods')
                ->whereIn('cid', $resoult['id'])
                ->group('cid')
                ->count();

            // 获取筛选项
            $card_filter = Db::name('column_filter')
                ->where('is_delete', 0)
                ->whereIn('card_id', $resoult['id'])
                ->order('sort asc,id desc')
                ->column('id,card_id,name,sort,add_method,auto_add_type,auto_add_content');
            foreach ($card_filter as &$v) {
                $v['auto_add_content'] = !empty($v['auto_add_content']) ? json_decode($v['auto_add_content'], true) : [];
            }

            $resoult['card_data_nums'] = $goods_nums;
            $resoult['filter']         = $card_filter ?? [];
        }
        return $resoult;
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param)
    {
        $time  = time();
        $param += [
            'updated_time' => $time,
            'createor_id'  => $param['operator_id'],
            'createor'     => $param['operator_name'],
            'updateor_id'  => $param['operator_id'],
            'updateor'     => $param['operator_name'],
        ];

        Db::startTrans();
        try {
            if (!empty($param['filter'])) {
                $filter = $param['filter'];
                unset($param['filter']);
            }

            $ColumnModel = new ColumnModel();

            if ($ColumnModel->where('name', $param['name'])->where('channel', $param['channel'])->value('id')) {
                excep('栏目名称已存在');
            }

            if (!$ColumnModel->save($param)) {
                excep('添加异常');
            }

            // 自动添加商品
            $add_method = 0;
            if (!empty($param['add_method']) && $param['add_method'] == 1) {
                $add_method = 1;
            }

            //筛选项
            if (isset($filter)) {
                // 查询筛选项
                $fids = Db::name('column_filter')->where('card_id', $ColumnModel->id)->column('id');

                $card_filter = $filterids = [];
                foreach ($filter as $key => $value) {
                    // 自动添加商品
                    if (!empty($value['add_method']) && $value['add_method'] == 1) {
                        $add_method = 1;
                        if (empty($value['auto_add_type']) || !is_numeric($value['auto_add_type'])) {
                            excep('请选择自动添加类型');
                        }
                        if (empty($value['auto_add_content']) || !is_array($value['auto_add_content'])) {
                            excep('请选择定义内容');
                        }
                        foreach ($value['auto_add_content'] as $v2) {
                            if (!isset($v2['id']) || !isset($v2['name'])) {
                                excep('定义内容格式错误');
                            }
                        }
                    }
                    $value['auto_add_content'] = !empty($value['auto_add_content']) ? json_encode($value['auto_add_content']) : '';

                    $filter_info = [
                        'name'             => $value['name'],
                        'card_id'          => $ColumnModel->id,
                        'sort'             => $key,
                        'update_at'        => $time,
                        'add_method'       => $value['add_method'] ?? 0,
                        'auto_add_type'    => $value['auto_add_type'] ?? 0,
                        'auto_add_content' => $value['auto_add_content'] ?? '',
                    ];
                    if (!empty($value['id'])) {
                        $filterids[] = $value['id'];
                        Db::name('column_filter')->where('id', $value['id'])
                            ->update($filter_info);
                    } else {
                        $filter_info['created_at'] = $time;
                        $card_filter[] = $filter_info;
                    }
                }

                if (!empty($card_filter)) {
                    Db::name('column_filter')->insertAll($card_filter);
                }

                // 删除旧筛选项
                $del_ids = [];
                foreach ($fids as $v) {
                    if (!in_array($v, $filterids)) {
                        $del_ids[] = $v;
                    }
                }
                if (!empty($del_ids)) {
                    Db::name('column_filter')->where('id', 'in', $del_ids)->update(['is_delete' => 1]);
                    Db::name('column_goods_filter')->where('filter_id', 'in', $del_ids)->delete();
                }
            }

            if ($add_method == 1) {
                // 把所有的在售商品按照规则自动添加商品到栏目
                AutomaticallyAddAllPeriod([$ColumnModel->id]);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param)
    {
        $time  = time();
        $param += [
            'updated_time' => time(),
            'updateor_id'  => $param['operator_id'],
            'updateor'     => $param['operator_name'],
        ];
        Db::startTrans();
        try {
            $filter = $param['filter'] ?? [];
            if (!empty($param['filter'])) {
                unset($param['filter']);
            }
            $ColumnModel = new ColumnModel();
            $info        = $ColumnModel::getInf([
                'where' => function ($query) use ($param) {
                    $query->where('id', $param['id']);
                },
                'field' => 'id,add_method,page_mode',
                'type'  => 'find'
            ]);
            if (!$info) {
                excep('栏目不存在');
            }
            if ($info['page_mode'] != $param['page_mode']) {
                excep('编辑时不能修改模式');
            }

            if (!empty($param['name'])) {
                if ($ColumnModel->where([['name', '=', $param['name']], ['channel', '=', $param['channel']], ['id', '<>', $param['id']]])->value('id')) {
                    excep('栏目名称已存在');
                }
            }


            if (!$info->save($param)) {
                excep('编辑异常');
            }

            // 自动添加商品
            $add_method = 0;
            if (!empty($param['add_method']) && $param['add_method'] == 1 && empty($info['add_method'])) {
                $add_method = 1;
            }

            // 查询筛选项
            $fids = Db::name('column_filter')->where('card_id', $param['id'])->column('id');
            $card_filter = $filterids = [];
            foreach ($filter as $key => $value) {
                // 自动添加商品
                if (!empty($value['add_method']) && $value['add_method'] == 1) {
                    if (empty($value['auto_add_type']) || !is_numeric($value['auto_add_type'])) {
                        excep('请选择自动添加类型');
                    }
                    if (empty($value['auto_add_content']) || !is_array($value['auto_add_content'])) {
                        excep('请选择定义内容');
                    }
                    foreach ($value['auto_add_content'] as $v2) {
                        if (!isset($v2['id']) || !isset($v2['name'])) {
                            excep('定义内容格式错误');
                        }
                    }
                }
                $value['auto_add_content'] = !empty($value['auto_add_content']) ? json_encode($value['auto_add_content']) : '';

                $filter_info = [
                    'name'             => $value['name'],
                    'card_id'          => $param['id'],
                    'sort'             => $key,
                    'update_at'        => $time,
                    'add_method'       => $value['add_method'] ?? 0,
                    'auto_add_type'    => $value['auto_add_type'] ?? 0,
                    'auto_add_content' => $value['auto_add_content'] ?? '',
                ];
                if (!empty($value['id'])) {
                    $filterids[] = $value['id'];
                    Db::name('column_filter')->where('id', $value['id'])
                        ->update($filter_info);
                } else {
                    if ($filter_info['add_method'] == 1) {
                        $add_method = 1;
                    }
                    $filter_info['created_at'] = $time;
                    $card_filter[] = $filter_info;
                }
            }
            if (!empty($card_filter)) {
                Db::name('column_filter')->insertAll($card_filter);
            }
            // 删除旧筛选项
            $del_ids = [];
            foreach ($fids as $v) {
                if (!in_array($v, $filterids)) {
                    $del_ids[] = $v;
                }
            }
            if (!empty($del_ids)) {
                Db::name('column_filter')->where('id', 'in', $del_ids)->update(['is_delete' => 1]);
                Db::name('column_goods_filter')->where('filter_id', 'in', $del_ids)->delete();
            }

            if ($add_method == 1) {
                // 把所有的在售商品按照规则自动添加商品到栏目
                AutomaticallyAddAllPeriod([$param['id']]);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }

        return true;
    }

    /**
     * 编辑状态
     * @return bool
     */
    static function updateStatus($param)
    {
        $time  = time();
        $param += [
            'updated_time' => $time,
            'updateor_id'  => $param['operator_id'],
            'updateor'     => $param['operator_name'],
        ];
        Db::startTrans();
        try {
            $ColumnModel = new ColumnModel();
            $info        = $ColumnModel::getInf([
                'where' => function ($query) use ($param) {
                    $query->where('id', $param['id']);
                },
                'field' => 'id,add_method,page_mode',
                'type'  => 'find'
            ]);
            if (!$info) {
                excep('栏目不存在');
            }


            if (!$info->save($param)) {
                excep('编辑异常');
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }

        return true;
    }

    static function clientlist($param){

        $where = function($query) use($param) {
            $query->where('status', 1);
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $p_channel_arr = ['7' => '20'];
                $param['channel'] = $p_channel_arr[$param['channel']] ?? $param['channel'];
                $query->where('channel', $param['channel']);
            }
            if (isset($param['client']) && strlen($param['client']) > 0) {
                // 鸿蒙返回安卓数据
                if ($param['client'] == 5) {
                    $param['client'] = 1;
                }
                $query->where('client', 'like', "%{$param['client']}%");
            }
        };

        // 列表
        $list = ColumnModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
            'type' => 'select'
        ]);

        $color = [
            ['#1A6AA6','#1A6AA6'],
            ['#673204','#673204'],
            ['#766600','#766600'],
            ['#08585B','#08585B'],
            ['#156C3E','#156C3E'],
            ['#8F3F35','#8F3F35'],
            ['#773C5E','#773C5E']
        ];
        foreach ($list as $k => &$value) {
            $value['path'] = '/activities-v3/ColumnActivity?id=' . $value['id'];
            $value['label_color'] = $color[$k] ?? $color[6];
        }

        // 总条数
        $total = ColumnModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);


        return [
            'list' => $list,
            'total' => $total
        ];
    }

    static function delect($param){

        Db::startTrans();
        try {
            ColumnModel::where('id',$param['id'])->delete();
            ColumnGoods::where('cid',$param['id'])->delete();
            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }
    }

    static function clientgoodslist($param){
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;

        $where = function($query) use($param) {
            $query->where('status', 1);
            $query->where('id', $param['id']);
        };

        // 列表
        $info = ColumnModel::getInf([
            'where' => $where,
            'type' => 'find'
        ]);

        //获取商品
        $column_goods = ColumnGoods::where('cid', $param['id'])
            ->where('status', 1)
            ->order('sort desc')
            ->column('id,period,short_name,short_desc,sort,created_at', 'period');
        $array_ids = array_column($column_goods, 'period');
        $from = 0;
        $size = 10000;
        $sort = [['sort' => 'desc']];
        
        // 指定字段排序
        if (!empty($param['sort_type']) && !empty($param['order'])) {
            $from = ($page - 1) * $limit;
            $size = $limit;
            if ($param['sort_type'] == 'purchased') {
                $sort = [
                    [
                        '_script' => [
                            'type' => 'number',
                            'script' => [
                                'lang' => 'painless',
                                'source' => "doc['purchased'].value + doc['vest_purchased'].value"
                            ],
                            'order' => $param['order']
                        ]
                    ]
                ];
            } else {
                $sort = [[$param['sort_type'] => $param['order']]];
            }
        }
        

        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];
        $es = new \app\service\elasticsearch\ElasticSearchService();
        $params = [
            'index' => ['periods'],
            'terms' => $therms,
            'page' => $from,
            'limit' => $size,
            'sort' => $sort,
        ];

        // 查询es商品信息
        $data = $es->getDocumentList($params);
        // $str_ids = implode(',', $array_ids);
        // 查询es商品信息
        // $resouce = AdExternal::getGoodListByids($str_ids);
        $goods_map = $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                // 优惠标签
                $v['discount_label'] = [];
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_map[$v['id']] = $v;
                $goods_list[] = $v;
            }
        }

        foreach ($goods_list as &$g) {
            $c_goods = $column_goods[$g['id']] ?? [];
            if (!empty($c_goods)) {
                //客户端详情页返回商品标题时，需要返回完整标题
                // if (!empty($c_goods['short_name'])) {
                //     $g['title'] = $c_goods['short_name'];
                // }
                if (!empty($c_goods['short_desc'])) {
                    $g['short_desc'] = $c_goods['short_desc'];
                }
                $g['sort_val'] = $c_goods['sort'];
                $g['created_at'] = $c_goods['created_at'];
            }
        }

        // 默认排序
        if ((empty($param['sort_type']) || $param['sort_type'] == 'sort') && !empty($goods_list)) {
            //排序处理
            $goods_list = self::sortProcessing($goods_list, $info);
            // 分页
            $goods_list = array_slice($goods_list, ($page - 1) * $limit, $limit);
            
        } else {
            // $time_field = ['predict_shipment_time','onsale_time'];
            // // 排序
            // if (!empty($param['sort_type']) && !empty($param['order'])) {
            //     // 已售排序
            //     if ($param['sort_type'] == 'purchased') {
            //         $param['sort_type'] = 'sold_num';
            //     }
            //     $value = array_column($goods_list, $param['sort_type']);
            //     if (in_array($param['sort_type'], $time_field)) {
            //         $value = array_map(function ($v) {
            //             return !empty($v) ? strtotime($v) : 0;
            //         }, $value);
            //     }
            //     $order = $param['order'] == 'desc' ? SORT_DESC : SORT_ASC;
            //     array_multisort($value, $order, $goods_list);
            // }
        }
        


        $info['goods_list'] = $goods_list;
        return $info;
    }

    static function filtergoodslist($param) {

        $where = function($query) use($param) {
            $query->where('status', 1);
            $query->where('id', $param['cid']);
        };

        // 列表
        $info = ColumnModel::getInf([
            'where' => $where,
            'type' => 'find'
        ]);

        //获取商品
        $column_goods = ColumnGoods::where('cid', $param['cid'])
            ->where('status', 1)
            ->order('sort desc')
            ->column('id,period,short_name,short_desc,sort,created_at', 'period');
        $array_ids = array_column($column_goods, 'period');
        // $from = 0;
        // $size = 10000;
        $sort = [['sort' => 'desc']];
        

        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];
        $es = new \app\service\elasticsearch\ElasticSearchService();
        $params = [
            'index' => ['periods'],
            'terms' => $therms,
            'limit' => 10000,
            'sort' => $sort,
        ];

        // 查询es商品信息
        $data = $es->getDocumentList($params);
        $goods_map = $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                // 优惠标签
                $v['discount_label'] = [];
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_map[$v['id']] = $v;
                $goods_list[] = $v;
            }
        }

        //商品筛选项
        $goods_ids = array_column($column_goods, 'id');
        $goods_filter = Db::name('column_goods_filter')
            ->whereIn('goods_id', $goods_ids)
            ->column('goods_id,filter_id');
        $filter_id = [];
        foreach ($goods_filter as $v) {
            $filter_id[$v['goods_id']][] = $v['filter_id'];
        }

        foreach ($goods_list as &$g) {
            $c_goods = $column_goods[$g['id']] ?? [];
            if (!empty($c_goods)) {
                $g['column_filter_id'] = $filter_id[$c_goods['id']] ?? [];

                //客户端详情页返回商品标题时，需要返回完整标题
                // if (!empty($c_goods['short_name'])) {
                //     $g['title'] = $c_goods['short_name'];
                // }
                if (!empty($c_goods['short_desc'])) {
                    $g['short_desc'] = $c_goods['short_desc'];
                }
                $g['sort_val'] = $c_goods['sort'];
                $g['created_at'] = $c_goods['created_at'];
            }
        }

        //排序处理
        $goods_list = self::sortProcessing($goods_list, $info);
        $column_goods = [];
        $total = 0;
        foreach ($goods_list as $g) {
            if (empty($g['column_filter_id'])) {
                continue;
            }
            foreach ($g['column_filter_id'] as $fid) {
                $total++;
                $column_goods[$fid][] = $g;
            }
        }

        //筛选项
        $column_filter = Db::name('column_filter')
            ->where('is_delete', 0)
            ->where('card_id', $param['cid'])
            ->order('sort asc')
            ->column('id,name');
        $list = [];
        foreach ($column_filter as $k => $v) {
            if (empty($column_goods[$v['id']])) {
                continue;
            }
            $v['goods'] = $column_goods[$v['id']];
            $list[] = $v;
        }
        
        return ['list' => $list, 'total' => $total];
    }

    static function goodslist($param) {
        $page = $param['page'] ?? 1;
        $limit = $param['limit'] ?? 10;
        $sort_type = $param['sort_type'] ?? 'sort';
        $order = $param['order'] ?? 'desc';

        $id = $param['activity_label_list'][0] ?? 0;
        $id_list = $param['activity_list'][0] ?? '';
        $id_list = $id_list == '48' ? '-1' : $id_list;

        if ($id_list == '-1') {
            $cid = Db::name('column')
                ->where([
                    'channel' => 20,
                    'status' => 1,
                ])
                ->column('id');
        } else {
            $cid = !empty($id_list) ? explode(',', $id_list) : [];
        }
        
        $info = [];
        if (!empty($id)) {
            $where = function($query) use($id) {
                $query->where('status', 1);
                $query->where('id', $id);
            };
            // 列表
            $info = ColumnModel::getInf([
                'where' => $where,
                'type' => 'find',
                'field' => 'id,goods_sort_type'
            ]);
            $cid = [$id];
        }
        
        //获取商品
        $column_goods = Db::name('column_goods')
            ->alias('cg')
            ->leftJoin('column c','c.id=cg.cid')
            ->whereIn('cg.cid', $cid)
            ->where('cg.status', 1)
            ->order('cg.sort desc')
            ->column('cg.id,cg.cid,cg.period,cg.short_name,cg.short_desc,cg.sort,cg.created_at,c.name');
        $period_column = [];
        foreach ($column_goods as $v) {
            $period_column[$v['period']][] = $v;
        }

        $array_ids = array_values(array_unique(array_column($column_goods, 'period')));

        // 分页
        if (!empty($info)) {
            $from = 0;
            $size = 10000;
        } else {
            $from = $page;
            $size = $limit;
        }
        
        // 排序
        if ($sort_type == 'purchased') {
            $sort = [
                [
                    '_script' => [
                        'type' => 'number',
                        'script' => [
                            'lang' => 'painless',
                            'source' => "doc['purchased'].value + doc['vest_purchased'].value"
                        ],
                        'order' => $order
                    ]
                ]
            ];
        } else {
            $sort = [[$sort_type => $order]];
        }
        $match = [];
        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];
        
        // 筛选
        if (!empty($param['filters'])) {
            foreach ($param['filters'] as $k => $v) {
                if ($k == 'product_category' && in_array('白酒', $v)) {
                    array_push($v, 
                        '酱香型',
                        '浓香型',
                        '清香型',
                        '米香型',
                        '凤香型',
                        '其他香型');
                }
                if (in_array($k, ['regions', 'current_winery', 'current_winery_map', 'label','winery'])) {
                    $k .= '.keyword';
                }
                $therms[] = [$k => $v];
            }
        }
        $range = [];
        if (!empty($param['price_gte']) && !empty($param['price_lte'])) {
            $range[] = [
                'price' => [
                    'gte' => floatval($param['price_gte']),
                    'lte' => floatval($param['price_lte']),
                ]
            ];
        }
        $es = new \app\service\elasticsearch\ElasticSearchService();
        // 查询es商品信息
        $data = $es->getDocumentList([
            'index' => ['periods'],
            'terms' => $therms,
            'range' => $range,
            'match' => $match,
            'page' => $from,
            'limit' => $size,
            'sort' => $sort,
        ]);
        $total = $data['total']['value'] ?? 0;
        $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                $s_column_goods = $period_column[$v['id']] ?? [];
                $column_id = [];
                foreach ($s_column_goods as $val) {
                    $column_id[] = $val['cid'];
                    if (!empty($val['short_desc'])) {
                        $v['short_desc'] = $val['short_desc'];
                    }
                    $v['sort_val'] = $val['sort'];
                    $v['created_at'] = $val['created_at'];
                }
                // 优惠标签
                $v['discount_label'] = [];
                $v['column_id'] = $column_id;
                $v['activity_id'] = $column_id[0] ?? 0;
                $v['activity_label_id'] = $column_id[0] ?? 0;
                // 栏目名称
                $v['special_activity_data']['activity_name'] = $s_column_goods[0]['name'] ?? '';
                $v['special_activity_data']['activity_label_name'] = $s_column_goods[0]['name'] ?? '';
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_list[] = $v;
            }
        }
        
        // 默认排序
        if (!empty($goods_list) && !empty($info) && $sort_type == 'sort') {
            //排序处理
            $goods_list = self::sortProcessing($goods_list, $info);
            // 分页
            $goods_list = array_slice($goods_list, ($page - 1) * $limit, $limit);
        }

        return ['list' => $goods_list,'total' => $total > 0 ? $total : 0];
    }
    
    // 排序处理
    static function sortProcessing($goods_list, $info)
    {
        $sort_group = [];
        foreach ($goods_list as $v) {
            $sort_group[$v['sort_val']][] = $v;
        }
        sort($sort_group, SORT_DESC);
        $goods_list_data = [];
        foreach ($sort_group as $v) {
            // 根据活动信息中的排序规则类型进行不同的排序处理
            switch ($info["goods_sort_type"]) {
                case "0": // 添加时间倒序
                    usort($v, function($a, $b) {
                        $timeA = strtotime($a["created_at"]);
                        $timeB = strtotime($b["created_at"]);
                        if ($timeA == $timeB) {
                            return 0;
                        }
                        return ($timeA < $timeB) ? -1 : 1;
                    });
                    break;
                case "1": // 上架时间倒序
                    usort($v, function($a, $b) {
                        $timeA = strtotime($a["onsale_time"]);
                        $timeB = strtotime($b["onsale_time"]);
                        if ($timeA == $timeB) {
                            return 0;
                        }
                        return ($timeA < $timeB) ? -1 : 1;
                    });
                    break;
                case "2": // 闪购排序值倒序
                    usort($v, function($a, $b) {
                        $timeA = $a["sort"];
                        $timeB = $b["sort"];
                        if ($timeA == $timeB) {
                            return 0;
                        }
                        return ($timeA < $timeB) ? -1 : 1;
                    });
                    break;
                case "3": // 随机
                    shuffle($v);
                    break;
            }
            $goods_list_data = array_merge($goods_list_data, $v);
        }
        return $goods_list_data;
    }

    /**
     * 栏目子项列表
     * @return array
     */
    static function columnfilterlist($param) {
        $where = function($query) use($param) {
            $query->where('is_delete', 0);

            $query->where('card_id', $param['cid']);
        };

        // 列表
        $list = ColumnFilter::getInf([
            'where' => $where,
            'order' => ['sort' => 'asc', 'id' => 'desc'],
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);

        // 总条数
    	$total = ColumnFilter::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }


    /**
     * 添加栏目子项
     * @return bool
     */
    static function columnfilteradd($param)
    {
        $time  = time();
        $param += [
            'created_at' => $time,
            'update_at' => $time,
        ];

        Db::startTrans();
        try {
            $ColumnFilter = new ColumnFilter();
            if ($ColumnFilter->where([['name', '=', $param['name']],['card_id', '=', $param['card_id']]])->value('id')) {
                excep('子项名称已存在');
            }

            $ColumnModel = new ColumnModel();

            $column_info = $ColumnModel->where('id', $param['card_id'])->find();
            if ($column_info['page_mode'] == 0) {
                excep('页面模式为浏览不支持添加子项');
            }

            if (!$ColumnFilter->save($param)) {
                excep('添加异常');
            }

            // 自动添加商品
            if (!empty($param['add_method']) && $param['add_method'] == 1) {
                // 把所有的在售商品按照规则自动添加商品到栏目
                AutomaticallyAddAllPeriod([$param['card_id']]);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 编辑栏目子项
     * @return bool
     */
    static function columnfilteredit($param)
    {
        $time  = time();
        $param += [
            'update_at' => $time,
        ];

        Db::startTrans();
        try {
            $ColumnFilter = new ColumnFilter();
            if ($ColumnFilter->where([['id', '<>', $param['id']],['name', '=', $param['name']],['card_id', '=', $param['card_id']]])->value('id')) {
                excep('子项名称已存在');
            }
            $column_filter = $ColumnFilter->where('id', $param['id'])->find();
            if (empty($column_filter)) {
                excep('子项不存在');
            }

            $ColumnModel = new ColumnModel();

            $column_info = $ColumnModel->where('id', $param['card_id'])->find();
            if ($column_info['page_mode'] == 0) {
                excep('页面模式为浏览不支持添加子项');
            }

            if (!$column_filter->save($param)) {
                excep('添加异常');
            }

            // 自动添加商品
            if (!empty($param['add_method']) && $param['add_method'] == 1 && $column_filter['add_method'] == 0) {
                // 把所有的在售商品按照规则自动添加商品到栏目
                AutomaticallyAddAllPeriod([$param['card_id']]);
            }

            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 删除栏目子项
     * @return bool
     */
    static function columnfilterdel($param)
    {
        Db::startTrans();
        try {
            $ColumnFilter = new ColumnFilter();
            $column_filter = $ColumnFilter->where('id', $param['id'])->find();
            if (empty($column_filter)) {
                excep('子项不存在');
            }

            $column_filter->save(['is_delete' => 1]);
            Db::commit();
        } catch (\Exception $exception) {
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }
}
