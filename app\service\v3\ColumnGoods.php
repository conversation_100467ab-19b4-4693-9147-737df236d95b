<?php

namespace app\service\v3;

use app\model\CardGoodsLive as CardGoodsLiveModel;
use app\model\Column as ColumnModel;
use app\model\ColumnGoods as ColumnGoodsModel;
use Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;

class ColumnGoods
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if (!empty($param['filter_id']) && !is_array($param['filter_id'])) {
                $param['filter_id'] = explode(',', $param['filter_id']);
            }
            if (!empty($param['add_type']) && is_array($param['add_type'])) {
                $query->whereIn('add_type', $param['add_type']);
            }

            $query->where('cid', $param['cid']);
            if (!empty($param['filter_id']) && is_array($param['filter_id'])) {
                if (in_array('-1', $param['filter_id'])) {//查询无子项
                    $query->where(function ($query) use ($param) {
                        $query->whereOr('id', 'NOT IN', function ($query) use ($param) {
                            $query->name('column_goods_filter')
                                ->where('card_id', $param['cid'])
                                ->field('goods_id');
                        });
                        $query->whereOr('id', 'IN', function ($query) use ($param) {
                            $filter_id = array_diff($param['filter_id'], ['-1']);
                            $query->name('column_goods_filter')
                                ->whereIn('filter_id', $filter_id)
                                ->field('goods_id');
                        });
                    });
                    

                } else {//查询子项
                    $query->whereIn('id', function ($query) use ($param) {
                        $query->name('column_goods_filter')
                            ->whereIn('filter_id', $param['filter_id'])
                            ->field('goods_id');
                    });
                }
            }
        };
        $order = ['sort' => 'desc', 'created_at' => 'desc'];
        if (!empty($param['sort'])) {
            $order = ['sort' => $param['sort']];
        }
        
        // 列表
        $column_goods = ColumnGoodsModel::getInf([
            'where' => $where,
            'order' => $order,
            'page' => 1,
        	'limit' => 10000,
            'type' => 'select'
    	]);

        $column_goods = $column_goods->toArray();

        if(!$column_goods)return ['list' => [], 'total' => 0];

        $ids = array_column($column_goods, 'id');
        //筛选项
        $card_goods_filter = Db::name('column_goods_filter')
            ->alias('cgf')
            ->leftJoin('column_filter cf','cf.id=cgf.filter_id')
            ->whereIn('cgf.goods_id', $ids)
            ->column('cgf.goods_id,cgf.filter_id,cf.name');
        $filter = $filter_ids = [];
        foreach ($card_goods_filter as $v) {
            $filter[$v['goods_id']][] = $v;
            $filter_ids[$v['goods_id']][] = $v['filter_id'];
        }


        $array_ids = array_column($column_goods,'period');
        $str_ids = implode(',', $array_ids);

        $therms = [];
        if (!empty($param['onsale_status']) && is_array($param['onsale_status'])) {
            $therms[] = ['onsale_status' => $param['onsale_status']];
        }
        $resouce = AdExternal::getGoodListByids($str_ids, $therms);
        $code = [];
        if (!empty($resouce['list'])) {
            foreach ($resouce['list'] as $v) {
                $code[$v['id']] = $v;
            }
        }

        $list = [];
        foreach($column_goods as $val) {
            $goods = $code[$val['period']] ?? [];
            if (empty($code[$val['period']])) {
                continue;
            }

            $val['onsale_status'] = 0;
            $val['filter'] = $filter[$val['id']] ?? [];
            $val['filter_id'] = $filter_ids[$val['id']] ?? [];
            $val['title'] = $goods['title'];
            $img = explode(',', $goods['product_img'])[0] ?? '';
            $val['image'] = !empty($img) ? env("ALIURL").$img : '';
            $val['onsale_status'] = $goods['onsale_status'];
            $list[] = $val;
        }

        // 总条数
        $total = count($list);
    	// $total = ColumnGoodsModel::getInf([
        //     'where' => $where,
        //     'type' => 'count'
        // ]);

        // 分页
        [$offsetMain, $limit] = pageHandle($param);
        $list = array_slice($list, $offsetMain, $limit);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {
        $time = time();
        $param += ['created_at' => $time];

        Db::startTrans();
        try {
            if (!empty($param['filter_id'])) {
                $filter_id = $param['filter_id'];
                unset($param['filter_id']);
            }
            if (ColumnGoodsModel::where([['period', '=', $param['period']], ['cid', '=', $param['cid']]])->value('id')){
                excep('期数已存在');
            }

            $CardGoodsLiveModel = new ColumnGoodsModel();
            if (!$CardGoodsLiveModel->save($param)) {
                excep('添加异常');
            }

            // 获取自增ID
            $id = $CardGoodsLiveModel->id;

            // 筛选项
            if (!empty($filter_id)) {
                $card_goods_filter = [];
                foreach ($filter_id as $value) {
                    $card_goods_filter[] = [
                        'goods_id' => $id,
                        'card_id' => $param['cid'],
                        'filter_id' => $value,
                        'created_at'  => $time,
                    ];
                }
                Db::name('column_goods_filter')->insertAll($card_goods_filter);
            }
            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }
        return true;
    }

    /**
     * 删除
     * @return bool
     */
    static function delete($param) {
        $info = ColumnGoodsModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('记录不存在');
        }

        if (!$info->delete()) {
            excep('删除异常');
        }
        return true;
    }

    /**
     * 修改
     * @param $param
     * @return CardGoodsLiveModel
     */
    static function update($param) {

        Db::startTrans();
        try {
            $info = ColumnGoodsModel::where('id', $param['id'])->find();
            if (!$info) {
                excep('商品记录不存在');
            }

            if (isset($param['filter_id'])) {
                $filter_id = $param['filter_id'];
                unset($param['filter_id']);
                // 删除筛选项
                Db::name('column_goods_filter')->where('goods_id', $info['id'])->delete();
            }

// 筛选项
            if (!empty($filter_id)) {
                $card_goods_filter = [];
                foreach ($filter_id as $value) {
                    $card_goods_filter[] = [
                        'goods_id' => $info['id'],
                        'card_id' => $info['cid'],
                        'filter_id' => $value,
                        'created_at'  => time(),
                    ];
                }
                Db::name('column_goods_filter')->insertAll($card_goods_filter);
            }

            if (ColumnGoodsModel::where([['period', '=', $param['period']], ['id', '<>', $param['id']], ['cid', '=', $param['cid']]])->value('id')) {
                excep('期数已存在');
            }

            $info->save($param);;
            Db::commit();
        }catch (\Exception $exception){
            Db::rollback();
            excep($exception->getMessage());
        }
        return $info;
    }

    static function getfilter($param) {
        return Db::name('column_filter')
            ->where('is_delete', 0)
            ->where('card_id', $param['id'])
            ->order('sort asc,id desc')
            ->column('id,name');
    }

    /**
     * 筛选项商品列表
     * @param  \think\Request  $request
     * @return array
     */
    static function getfiltergoodslist($param) {

        $limit     = $param['limit'] ?? 10;
        $page      = $param['page'] ?? 1;
        // $pagestart = ($page - 1) * $limit;

        $where = function($query) use($param) {
            // $query->where('status', 1);
            $query->where('id', $param['cid']);
        };

        // 列表
        $info = ColumnModel::getInf([
            'where' => $where,
            'type' => 'find'
        ]);
        if (empty($info)) {
            return ['list' => [], 'total' => 0];
        }

        //获取商品
        $column_goods = ColumnGoodsModel::where([
                ['cid', '=', $param['cid']],
                ['status', '=', 1],
            ])
            ->where('id', 'IN', function ($query) use ($param) {
                $query->name('column_goods_filter')->where([
                    ['card_id', '=', $param['cid']],
                    ['filter_id', '=', $param['filter_id']],
                ])->field('goods_id');
            })
            ->order('sort desc')
            ->column('id,period,short_name,short_desc,sort,created_at', 'period');

        $array_ids = array_column($column_goods, 'period');
        $sort = [['sort' => 'desc']];
        

        $therms = [
            ['id' => $array_ids],
            // 在售中商品
            ['onsale_status' => [1, 2]]
        ];
        $es = new \app\service\elasticsearch\ElasticSearchService();
        $params = [
            'index' => ['periods'],
            'terms' => $therms,
            // 'page' => $page,
            'limit' => 10000,
            'sort' => $sort,
        ];

        // 查询es商品信息
        $data = $es->getDocumentList($params);
        $goods_map = $goods_list = [];
        if (!empty($data['data'])) {
            foreach ($data['data'] as $v) {
                // 优惠标签
                $v['discount_label'] = [];
                // 已售数量
                $v['sold_num'] = intval($v['vest_purchased'] + $v['purchased']);
                $goods_map[$v['id']] = $v;
                $goods_list[] = $v;
            }
        }
        if (empty($goods_list)) {
            return ['list' => [], 'total' => 0];
        }

        //商品筛选项
        $goods_ids = array_column($column_goods, 'id');
        $goods_filter = Db::name('column_goods_filter')
            ->whereIn('goods_id', $goods_ids)
            ->column('goods_id,filter_id');
        $filter_id = [];
        foreach ($goods_filter as $v) {
            $filter_id[$v['goods_id']][] = $v['filter_id'];
        }

        foreach ($goods_list as &$g) {
            $c_goods = $column_goods[$g['id']] ?? [];
            if (!empty($c_goods)) {
                $g['column_filter_id'] = $filter_id[$c_goods['id']] ?? [];

                //客户端详情页返回商品标题时，需要返回完整标题
                // if (!empty($c_goods['short_name'])) {
                //     $g['title'] = $c_goods['short_name'];
                // }
                if (!empty($c_goods['short_desc'])) {
                    $g['short_desc'] = $c_goods['short_desc'];
                }
                $g['sort_val'] = $c_goods['sort'];
                $g['created_at'] = $c_goods['created_at'];
            }
        }

        //排序处理
        $goods_list = \app\service\v3\Column::sortProcessing($goods_list, $info);
        
        $total = count($goods_list);

        $goods_list = array_slice($goods_list, ($page - 1) * $limit, $limit);
        
        return ['list' => $goods_list, 'total' => $total];
    }

}
