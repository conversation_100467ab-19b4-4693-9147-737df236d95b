<?php

namespace app\service\v3;

class Common
{
    /**
     * 频道
     * @return bool
     */
    static function channel($param) {
        if (!isset($param['type']) || !$param['type']) {
            excep('type值不能为空');
        }

//        $channel = [];//ad_capsule_channel

        $channel = config('config.ad_capsule_channel');
        return $channel;
    }

    static function chackNewUser($param){
        $str = '';
        foreach ($param as $k=>$v){
            $str .= $k."=".$v;
        }
        $url = env("ITEM.USER_URL").'/user/v3/profile/getUserInfo?info_type=1&field=is_new_user&'.$str;
        $data = httpCurl($url);

//        var_dump($data);
        $userInfo =  (array)json_decode($data,true);
        if($userInfo != false && $userInfo['error_code'] == 0){//正常返回
//            echo json_encode($userInfo['data']);
            if(isset($userInfo['data']['list'][0]['is_new_user'])){
                if($userInfo['data']['list'][0]['is_new_user']==1){//新用户
                    return 1;//新用户
                }
            }
            return 2;//老用户
        }else{//数据不存在，未登录
            return 3;//未登录
        }
    }

    /**
     * 判断用户是否过滤商品
     * @param $param
     * @return bool true 过滤 false  不过滤
     */
    static function restrictUser($param){

        $url = env("ITEM.USER_URL").'/user/v3/profile/getUserInfo?info_type=1&field=goods_restrict,user_level&uid='.$param['ids'];
        $data = httpCurl($url);
        $userInfo =  (array)json_decode($data,true);
//        var_dump($userInfo['data']);
        if($userInfo != false && $userInfo['error_code'] == 0){//正常返回

            if($userInfo['data']['list'][0]['goods_restrict'] == 1 || $userInfo['data']['list'][0]['user_level'] <1 ){
                return true;
            }else{
                return false;
            }
        }
        return true;
    }
}
