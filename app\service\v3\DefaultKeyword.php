<?php

namespace app\service\v3;

use app\model\DefaultKeyword as Default<PERSON>eywordModel;

class DefaultKeyword
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        // 列表
        $list = DefaultKeywordModel::getInf([
        	'order' => ['updated_at' => 'desc'],
            'type' => 'select'
    	]);

        /*foreach($list as $key => $val) {
            $list[$key]['updated_at'] = date('Y-m-d H:i:s', $val['updated_at']);

           // 创建人
           $list[$key]['operator_name'] = '';
           if ($val['operator_id'] != 0) {
               $response = json_decode(httpCurl(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info?admin_id=' . $val['operator_id']), true);
               if (!isset($response['error_code']) || $response['error_code'] != 0) {
                   excep($response['error_msg']);
               }

               $list[$key]['operator_name'] = $response['data'][$val['operator_id']]['realname'];
           }
        }*/

        return [
        	'list' => $list
        ];
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $info = DefaultKeywordModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            // 'field' => 'id',
            'order' => ['updated_at' => 'desc'],
            'type' => 'find'
        ]);
        if (!$info) {
            excep('关键词不存在');
        }

//        $param['updated_at'] = time();
        if (!$info->save($param)) {
            excep('编辑异常');
        }
        return true;
    }

    /**
     * 根据频道获取关键词
     * @return array
     */
    static function getKeyword($param) {
        // 列表
        $info = DefaultKeywordModel::getInf([
            'where' => function($query) use($param) {
                $query->where('type', $param['type']);
            },
            'order' => ['updated_at' => 'desc'],
        	'field' => 'id, keyword',
            'type' => 'find'
    	]);
        if (!$info) {
            excep('获取关键词异常');
        }

        return $info;
    }
}
