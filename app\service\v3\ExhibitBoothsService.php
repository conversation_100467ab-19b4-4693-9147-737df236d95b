<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\ExhibitBooths;
use app\model\ExhibitBrands;
use app\validate\ExhibitBoothsValidate;
use think\facade\Db;
use think\facade\Log;

/**
 * 展台表
 * Class ExhibitBoothsService
 * @package app\service\v3
 */
class ExhibitBoothsService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new ExhibitBooths;
        $this->validate    = ExhibitBoothsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2024/05/23 18:06
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键ID
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键ID
            }
            #endregion

            #region LIKE name 展台名称
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', "LIKE", "{$param['name']}%"); //展台名称
            }
            #endregion

            #region LIKE number 展台编号
            if (isset($param['number']) && strlen($param['number']) > 0) {
                $query->where('number', "LIKE", "{$param['number']}%"); //展台编号
            }
            #endregion

            #region EQ exhibition_id 展会ID
            if (isset($param['exhibition_id']) && strlen($param['exhibition_id']) > 0) {
                $query->where('exhibition_id', "=", $param['exhibition_id']); //展会ID
            }
            #endregion

            #region >= start_updated_at 开始更新时间
            if (isset($param['start_updated_at']) && strlen($param['start_updated_at']) > 0) {
                $query->whereTime('updated_at', '>=', $param['start_updated_at']); //开始更新时间
            }
            #endregion

            #region < end_updated_at 结束更新时间
            if (isset($param['end_updated_at']) && strlen($param['end_updated_at']) > 0) {
                $query->whereTime('updated_at', '<', $param['end_updated_at']); //结束更新时间
            }
            #endregion

            #region EQ operator_id 操作人ID
            if (isset($param['operator_id']) && strlen($param['operator_id']) > 0) {
//                $query->where('operator_id', "=", $param['operator_id']); //操作人ID
            }
            #endregion

        };
    }

    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
        Db::startTrans();
        try {
            $res = 0;
            if (ExhibitBrands::where('booth_id', $param['id'])->count() > 0) {
                throw new \Exception('展台被品牌引用,无法删除');
            }
            $model = $this->model->where('id', $param['id'])->find();
            if ($model) {
                $res = $model->delete() ? 1 : 0;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }

}



