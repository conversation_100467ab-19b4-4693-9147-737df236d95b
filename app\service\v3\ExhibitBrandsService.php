<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\ExhibitBrands;
use app\model\ExhibitProducts;
use app\validate\ExhibitBrandsValidate;
use think\facade\Db;
use think\facade\Log;

/**
 * 品牌表
 * Class ExhibitBrandsService
 * @package app\service\v3
 */
class ExhibitBrandsService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new ExhibitBrands;
        $this->validate    = ExhibitBrandsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2024/05/23 18:08
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键ID
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键ID
            }
            #endregion

            #region LIKE name 品牌名称
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', "LIKE", "{$param['name']}%"); //品牌名称
            }
            #endregion

            #region LIKE logo_url 品牌Logo URL
            if (isset($param['logo_url']) && strlen($param['logo_url']) > 0) {
                $query->where('logo_url', "LIKE", "{$param['logo_url']}%"); //品牌Logo URL
            }
            #endregion

            #region EQ is_own_brand 是否为自有品牌
            if (isset($param['is_own_brand']) && strlen($param['is_own_brand']) > 0) {
                $query->where('is_own_brand', "=", $param['is_own_brand']); //是否为自有品牌
            }
            #endregion

            #region EQ exhibition_id 展会ID,外键,关联到展会表
            if (isset($param['exhibition_id']) && strlen($param['exhibition_id']) > 0) {
                $query->where('exhibition_id', "=", $param['exhibition_id']); //展会ID，外键，关联到展会表
            }
            #endregion

            #region >= start_updated_at 开始更新时间
            if (isset($param['start_updated_at']) && strlen($param['start_updated_at']) > 0) {
                $query->whereTime('updated_at', '>=', $param['start_updated_at']); //开始更新时间
            }
            #endregion

            #region < end_updated_at 结束更新时间
            if (isset($param['end_updated_at']) && strlen($param['end_updated_at']) > 0) {
                $query->whereTime('updated_at', '<', $param['end_updated_at']); //结束更新时间
            }
            #endregion

            #region EQ operator_id 操作人ID
            if (isset($param['operator_id']) && strlen($param['operator_id']) > 0) {
//                $query->where('operator_id', "=", $param['operator_id']); //操作人ID
            }
            #endregion

            #region EQ booth_id 展台ID,外键,关联到展台表
            if (isset($param['booth_id']) && strlen($param['booth_id']) > 0) {
                $query->where('booth_id', "=", $param['booth_id']); //展台ID，外键，关联到展台表
            }
            #endregion

            #region LIKE brand_about_section_image_url 关于品牌栏目图片URL
            if (isset($param['brand_about_section_image_url']) && strlen($param['brand_about_section_image_url']) > 0) {
                $query->where('brand_about_section_image_url', "LIKE", "{$param['brand_about_section_image_url']}%"); //关于品牌栏目图片URL
            }
            #endregion

            #region LIKE brand_description_text_color 描述文字颜色值
            if (isset($param['brand_description_text_color']) && strlen($param['brand_description_text_color']) > 0) {
                $query->where('brand_description_text_color', "LIKE", "{$param['brand_description_text_color']}%"); //描述文字颜色值
            }
            #endregion

            #region LIKE brand_middle_color_value 品牌中部颜色值
            if (isset($param['brand_middle_color_value']) && strlen($param['brand_middle_color_value']) > 0) {
                $query->where('brand_middle_color_value', "LIKE", "{$param['brand_middle_color_value']}%"); //品牌中部颜色值
            }
            #endregion

            #region LIKE brand_bottom_image_url 品牌底部图片URL
            if (isset($param['brand_bottom_image_url']) && strlen($param['brand_bottom_image_url']) > 0) {
                $query->where('brand_bottom_image_url', "LIKE", "{$param['brand_bottom_image_url']}%"); //品牌底部图片URL
            }
            #endregion

            #region LIKE top_image_url 顶部图片URL
            if (isset($param['top_image_url']) && strlen($param['top_image_url']) > 0) {
                $query->where('top_image_url', "LIKE", "{$param['top_image_url']}%"); //顶部图片URL
            }
            #endregion

        };
    }


    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
        Db::startTrans();
        try {
            $res = 0;
            if (ExhibitProducts::where('brand_id', $param['id'])->count() > 0) {
                throw new \Exception('品牌被商品引用,无法删除');
            }
            $model = $this->model->where('id', $param['id'])->find();
            if ($model) {
                $res = $model->delete() ? 1 : 0;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }

}



