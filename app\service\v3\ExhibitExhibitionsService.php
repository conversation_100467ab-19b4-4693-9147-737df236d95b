<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\ExhibitBooths;
use app\model\ExhibitExhibitions;
use app\validate\ExhibitExhibitionsValidate;
use think\facade\Db;
use think\facade\Log;

/**
 * 展会表
 * Class ExhibitExhibitionsService
 * @package app\service\v3
 */
class ExhibitExhibitionsService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new ExhibitExhibitions;
        $this->validate    = ExhibitExhibitionsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2024/05/23 17:45
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键ID
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键ID
            }
            #endregion

            #region LIKE name 展会名称
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', "LIKE", "{$param['name']}%"); //展会名称
            }
            #endregion

            #region >= start_start_date 开始展会开始日期
            if (isset($param['start_start_date']) && strlen($param['start_start_date']) > 0) {
                $query->whereTime('start_date', '>=', $param['start_start_date']); //开始展会开始日期
            }
            #endregion

            #region < end_start_date 结束展会开始日期
            if (isset($param['end_start_date']) && strlen($param['end_start_date']) > 0) {
                $query->whereTime('start_date', '<', $param['end_start_date']); //结束展会开始日期
            }
            #endregion

            #region >= start_end_date 开始展会结束日期
            if (isset($param['start_end_date']) && strlen($param['start_end_date']) > 0) {
                $query->whereTime('end_date', '>=', $param['start_end_date']); //开始展会结束日期
            }
            #endregion

            #region < end_end_date 结束展会结束日期
            if (isset($param['end_end_date']) && strlen($param['end_end_date']) > 0) {
                $query->whereTime('end_date', '<', $param['end_end_date']); //结束展会结束日期
            }
            #endregion

            #region LIKE top_image_url 顶部图片URL
            if (isset($param['top_image_url']) && strlen($param['top_image_url']) > 0) {
                $query->where('top_image_url', "LIKE", "{$param['top_image_url']}%"); //顶部图片URL
            }
            #endregion

            #region LIKE featured_products_button_image_url 精选产品按钮图片URL
            if (isset($param['featured_products_button_image_url']) && strlen($param['featured_products_button_image_url']) > 0) {
                $query->where('featured_products_button_image_url', "LIKE", "{$param['featured_products_button_image_url']}%"); //精选产品按钮图片URL
            }
            #endregion

            #region LIKE featured_products_button_h_image_url 精选产品按钮图片URL(选中)
            if (isset($param['featured_products_button_h_image_url']) && strlen($param['featured_products_button_h_image_url']) > 0) {
                $query->where('featured_products_button_h_image_url', "LIKE", "{$param['featured_products_button_h_image_url']}%"); //精选产品按钮图片URL(选中)
            }
            #endregion

            #region LIKE exhibiting_brands_button_image_url 参展品牌按钮图片URL
            if (isset($param['exhibiting_brands_button_image_url']) && strlen($param['exhibiting_brands_button_image_url']) > 0) {
                $query->where('exhibiting_brands_button_image_url', "LIKE", "{$param['exhibiting_brands_button_image_url']}%"); //参展品牌按钮图片URL
            }
            #endregion

            #region LIKE exhibiting_brands_button_h_image_url 参展品牌按钮图片URL(选中)
            if (isset($param['exhibiting_brands_button_h_image_url']) && strlen($param['exhibiting_brands_button_h_image_url']) > 0) {
                $query->where('exhibiting_brands_button_h_image_url', "LIKE", "{$param['exhibiting_brands_button_h_image_url']}%"); //参展品牌按钮图片URL(选中)
            }
            #endregion

            #region LIKE featured_products_section_image_url 精选产品栏目图片URL
            if (isset($param['featured_products_section_image_url']) && strlen($param['featured_products_section_image_url']) > 0) {
                $query->where('featured_products_section_image_url', "LIKE", "{$param['featured_products_section_image_url']}%"); //精选产品栏目图片URL
            }
            #endregion

            #region LIKE exhibiting_brands_section_image_url 参展品牌栏目图片URL
            if (isset($param['exhibiting_brands_section_image_url']) && strlen($param['exhibiting_brands_section_image_url']) > 0) {
                $query->where('exhibiting_brands_section_image_url', "LIKE", "{$param['exhibiting_brands_section_image_url']}%"); //参展品牌栏目图片URL
            }
            #endregion

            #region LIKE bottom_image_url 底部图片URL
            if (isset($param['bottom_image_url']) && strlen($param['bottom_image_url']) > 0) {
                $query->where('bottom_image_url', "LIKE", "{$param['bottom_image_url']}%"); //底部图片URL
            }
            #endregion

            #region LIKE middle_color_value 中部颜色值
            if (isset($param['middle_color_value']) && strlen($param['middle_color_value']) > 0) {
                $query->where('middle_color_value', "LIKE", "{$param['middle_color_value']}%"); //中部颜色值
            }
            #endregion

            #region LIKE brand_about_section_image_url 关于品牌栏目图片URL
            if (isset($param['brand_about_section_image_url']) && strlen($param['brand_about_section_image_url']) > 0) {
                $query->where('brand_about_section_image_url', "LIKE", "{$param['brand_about_section_image_url']}%"); //关于品牌栏目图片URL
            }
            #endregion

            #region LIKE brand_description_text_color 描述文字颜色值
            if (isset($param['brand_description_text_color']) && strlen($param['brand_description_text_color']) > 0) {
                $query->where('brand_description_text_color', "LIKE", "{$param['brand_description_text_color']}%"); //描述文字颜色值
            }
            #endregion

            #region LIKE brand_middle_color_value 品牌中部颜色值
            if (isset($param['brand_middle_color_value']) && strlen($param['brand_middle_color_value']) > 0) {
                $query->where('brand_middle_color_value', "LIKE", "{$param['brand_middle_color_value']}%"); //品牌中部颜色值
            }
            #endregion

            #region LIKE brand_bottom_image_url 品牌底部图片URL
            if (isset($param['brand_bottom_image_url']) && strlen($param['brand_bottom_image_url']) > 0) {
                $query->where('brand_bottom_image_url', "LIKE", "{$param['brand_bottom_image_url']}%"); //品牌底部图片URL
            }
            #endregion

            #region >= start_updated_at 开始更新时间
            if (isset($param['start_updated_at']) && strlen($param['start_updated_at']) > 0) {
                $query->whereTime('updated_at', '>=', $param['start_updated_at']); //开始更新时间
            }
            #endregion

            #region < end_updated_at 结束更新时间
            if (isset($param['end_updated_at']) && strlen($param['end_updated_at']) > 0) {
                $query->whereTime('updated_at', '<', $param['end_updated_at']); //结束更新时间
            }
            #endregion

            #region EQ operator_id 操作人ID
            if (isset($param['operator_id']) && strlen($param['operator_id']) > 0) {
//                $query->where('operator_id', "=", $param['operator_id']); //操作人ID
            }
            #endregion

        };
    }

    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function del($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 删除
        Db::startTrans();
        try {
            $res = 0;
            if (ExhibitBooths::where('exhibition_id', $param['id'])->count() > 0) {
                throw new \Exception('展会被展台引用,无法删除');
            }
            $model = $this->model->where('id', $param['id'])->find();
            if ($model) {
                $res = $model->delete() ? 1 : 0;
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($res);
    }

}



