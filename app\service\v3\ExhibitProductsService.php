<?php

namespace app\service\v3;

use app\BaseService2;
use app\ErrorCode;
use app\model\ExhibitProducts;
use app\service\es\Es;
use app\validate\ExhibitProductsValidate;
use think\Exception;
use think\facade\Db;
use think\facade\Log;

/**
 * 商品表
 * Class ExhibitProductsService
 * @package app\service\v3
 */
class ExhibitProductsService extends BaseService2
{
    public function __construct()
    {
        $this->model       = new ExhibitProducts;
        $this->validate    = ExhibitProductsValidate::class;
        $this->select_with = [];
    }


    /**
     * @方法描述: 筛选条件
     * <AUTHOR>
     * @Date 2024/05/23 18:08
     * @param $param
     * @return \Closure
     */
    protected function builderWhere($param)
    {
        return function ($query) use ($param) {

            #region EQ id 主键ID
            if (isset($param['id']) && strlen($param['id']) > 0) {
                $query->where('id', "=", $param['id']); //主键ID
            }
            #endregion

            #region LIKE name 商品名称
            if (isset($param['name']) && strlen($param['name']) > 0) {
                $query->where('name', "LIKE", "{$param['name']}%"); //商品名称
            }
            #endregion

            #region LIKE product_title 商品标题
            if (isset($param['product_title']) && strlen($param['product_title']) > 0) {
                $query->where('product_title', "LIKE", "{$param['product_title']}%"); //商品标题
            }
            #endregion

            #region EQ price 商品价格
            if (isset($param['price']) && strlen($param['price']) > 0) {
                $query->where('price', "=", $param['price']); //商品价格
            }
            #endregion

            #region EQ market_price 市场价
            if (isset($param['market_price']) && strlen($param['market_price']) > 0) {
                $query->where('market_price', "=", $param['market_price']); //市场价
            }
            #endregion

            #region LIKE product_image_url 产品图片URL
            if (isset($param['product_image_url']) && strlen($param['product_image_url']) > 0) {
                $query->where('product_image_url', "LIKE", "{$param['product_image_url']}%"); //产品图片URL
            }
            #endregion

            #region EQ is_featured 是否精选
            if (isset($param['is_featured']) && strlen($param['is_featured']) > 0) {
                $query->where('is_featured', "=", $param['is_featured']); //是否精选
            }
            #endregion

            #region EQ brand_id 品牌ID,外键,关联到品牌表
            if (isset($param['brand_id']) && strlen($param['brand_id']) > 0) {
                $query->where('brand_id', "=", $param['brand_id']); //品牌ID，外键，关联到品牌表
            }
            #endregion

            #region EQ exhibition_id 展会ID,外键,关联到展会表
            if (isset($param['exhibition_id']) && strlen($param['exhibition_id']) > 0) {
                $query->where('exhibition_id', "=", $param['exhibition_id']); //展会ID，外键，关联到展会表
            }
            #endregion

            #region EQ operator_id 操作人ID
            if (isset($param['operator_id']) && strlen($param['operator_id']) > 0) {
//                $query->where('operator_id', "=", $param['operator_id']); //操作人ID
            }
            #endregion

            #region EQ period 商品周期
            if (isset($param['period']) && strlen($param['period']) > 0) {
                $query->where('period', "=", $param['period']); //商品周期
            }
            #endregion

            #region EQ booth_id 展台ID,外键,关联到展台表
            if (isset($param['booth_id']) && strlen($param['booth_id']) > 0) {
                $query->where('booth_id', "=", $param['booth_id']); //展台ID，外键，关联到展台表
            }
            #endregion

        };
    }


    /**
     * @方法描述: 插入一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function add($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 插入数据
        Db::startTrans();
        try {
            if ($this->model->where('period', $param['period'])->where('brand_id', $param['brand_id'])->count() > 0) {
                throw new \Exception('重复添加,品牌已存在该商品');
            }

            $period                 = Es::name(Es::PERIODS)->where([['id', '==', $param['period']]])->field('id,periods_type,onsale_status')->find();
            $param['period_type']   = $period['periods_type'] ?? '';
            $param['onsale_status'] = $period['onsale_status'] ?? 0;
            $this->model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('插入数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('插入数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    /**
     * @方法描述: 更新一条数据
     * <AUTHOR>
     * @Date 2022/8/10 14:37
     * @param $param
     * @return bool|mixed
     */
    public function edit($param)
    {
        #region验证
        $validate = $this->check(__FUNCTION__, $param);
        if ($validate !== true) {
            return $validate;
        }
        #endregion

        //region 修改
        Db::startTrans();
        try {
            if ($this->model->where('id', '<>', $param['id'])->where('period', $param['period'])->where('brand_id', $param['brand_id'])->count() > 0) {
                throw new \Exception('重复添加,品牌已存在该商品');
            }

//            $period               = Es::name(Es::PERIODS)->where([['id', '==', $param['period']]])->field('id,periods_type')->find();
//            $param['period_type'] = $period['periods_type'] ?? '';
            unset($param['onsale_status']);

            $model = $this->model->find($param['id']);
            if ($model === null) {
                throw new \Exception("未找到数据, 请检查参数.");
            }

            $model->save($param);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success();
    }

    public function quantitySold($param)
    {
        $data = [];
        //region 修改
        Db::startTrans();
        try {
            if (!empty($param['ids'])) {
                $ids     = explode(',', $param['ids'] ?? '');
                $periods = Es::name(Es::PERIODS)->where([['id', 'in', $ids]])->field('id,purchased,vest_purchased')->select()->toArray();
                $periods = array_column($periods, null, 'id');
                foreach ($ids as $id) {
                    $data[] = [
                        'id'        => intval($id),
                        'sold_nums' => intval(bcadd(($periods[$id]['purchased'] ?? 0), ($periods[$id]['vest_purchased'] ?? 0))),
                    ];
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('更新数据失败: ' . $e->getMessage() . ' SQL: ' . Db::getLastSql());
            return $this->failed('更新数据失败: ' . $e->getMessage(), ErrorCode::EXEC_ERROR);
        }
        //endregion

        return $this->success($data);
    }

    public function sync($param)
    {
        Log::write('进入同步更新产品售卖状态: ' . json_encode($param));
        if (empty($param['period'])) {
            throw new Exception('期数不能为空');
        }
        if (empty($param['onsale_status'])) {
            throw new Exception('期数不能为空');
        }

        $products = ExhibitProducts::where('period', $param['period'])->select();

        foreach ($products as $product) {
            if (!empty($param['table'])) {
                $period_info = Db::table("vh_commodities.{$param['table']}")
                    ->where('id', $param['period'])
                    ->field('id,title,brief,market_price,price,product_img')
                    ->find();
            }
            try {
                $product->onsale_status = $param['onsale_status'];
                if (!empty($period_info)) {
                    $product_image_url          = explode(',', $period_info['product_img'])[0] ?? '';
                    $product_image_url          = empty($product_image_url) ? '' : env('ALIURL') . $product_image_url;
                    $product->product_title     = $period_info['title'];
                    $product->description       = $period_info['brief'];
                    $product->price             = $period_info['price'];
                    $product->market_price      = $period_info['market_price'];
                    $product->product_image_url = $product_image_url;
                }
                $product->save();
            } catch (\Exception $e) {
                Log::write('同步更新产品售卖状态 错误: ' . ($product['id'] ?? '') . ' ' . $e->getMessage());
            }
        }
        return $this->success();
    }

}



