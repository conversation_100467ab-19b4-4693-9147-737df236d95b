<?php

namespace app\service\v3;

use app\service\es\Es;
use app\service\v3\AdExternal;
use think\cache\driver\Redis;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;
use think\Model;

class Game
{

    /**
     * 抽奖更新参数配置
     * @param array $params
     * @return int|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRotaryDraw(array $params)
    {
        // 更新数据库
        $result = Db::name('rotary_draw')->where('id', $params['id'])->update($params);
        // 更新失败
        if (!$result) {
            return $result;
        }
        // 更新成功更新缓存
        $list = Db::name('rotary_draw')->order('sort', 'asc')->select()->toArray();
        if (!empty($list)) {
            $list = json_encode($list);
        }
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        $redis->set('rotary_draw', $list);
        return $result;
    }

    public function rabbitUpdateRotaryDraw(array $params)
    {
        // 更新数据库
        $result = Db::name('rabbit_rotary_draw')->where('id', $params['id'])->update($params);
        // 更新失败
        if (!$result) {
            return $result;
        }
        // 更新成功更新缓存
        $list = Db::name('rabbit_rotary_draw')->order('sort', 'asc')->select()->toArray();
        if (!empty($list)) {
            $list = json_encode($list);
        }
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        $redis->set('rabbit_rotary_draw', $list);
        return $result;
    }

    /**
     * 更新黑名单抽奖配置
     * @param array $params
     * @return int
     * @throws \RedisException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRotaryDrawBlacklist(array $params)
    {
        // 更新数据库
        $result = Db::name('rotary_draw_blacklist')->where('id', $params['id'])->update($params);
        // 更新失败
        if (!$result) {
            return $result;
        }
        // 更新成功更新缓存
        $list = Db::name('rotary_draw_blacklist')->order('sort', 'asc')->select()->toArray();
        if (!empty($list)) {
            $list = json_encode($list);
        }
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        $redis->set('rotary_draw_blacklist', $list);
        return $result;
    }

    /**
     * 抽奖更新兔头参数配置
     * @param array $params
     * @return int|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRabbitRotaryDraw(array $params)
    {
        // 更新数据库
        $result = Db::name('rabbit_rotary_draw')->where('id', $params['id'])->update($params);
        // 更新失败
        if (!$result) {
            return $result;
        }
        // 更新成功更新缓存
        $list = Db::name('rabbit_rotary_draw')->order('sort', 'asc')->select()->toArray();
        if (!empty($list)) {
            $list = json_encode($list);
        }
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        $redis->set('rabbit_rotary_draw', $list);
        return $result;
    }

    /**
     * 获取幸运转盘配置
     */
    public function getRotaryDraw()
    {

//        $redis = new Redis(Config('cache')['stores']['redis']);
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        // 查询缓存
        $list = $redis->get('rotary_draw');
        // 如果缓存不存在重新写入缓存
        if (empty($list)) {
            $list = Db::name('rotary_draw')->order('sort', 'asc')->select()->toArray();
            if (!empty($list)) {
                $list = json_encode($list);
            }
            $redis->set('rotary_draw', $list);
        }
        if ($list) {
            $list = json_decode($list, true);
            if (!empty($list)) {
                foreach ($list as &$val) {
                    $val['image'] = env('ALIURL') . $val['image'];
                }
            }
        }
        return $list;
    }

    public function getRotaryDrawBlacklist()
    {

//        $redis = new Redis(Config('cache')['stores']['redis']);
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        // 查询缓存
        $list = $redis->get('rotary_draw_blacklist');
        // 如果缓存不存在重新写入缓存
        if (empty($list)) {
            $list = Db::name('rotary_draw_blacklist')->order('sort', 'asc')->select()->toArray();
            if (!empty($list)) {
                $list = json_encode($list);
            }
            $redis->set('rotary_draw_blacklist', $list);
        }
        if ($list) {
            $list = json_decode($list, true);
            if (!empty($list)) {
                foreach ($list as &$val) {
                    $val['image'] = env('ALIURL') . $val['image'];
                }
            }
        }
        return $list;
    }

    /**
     * 获取兔头幸运转盘配置
     */
    public function getRabbitRotaryDraw()
    {
//        $redis = new Redis(Config('cache')['stores']['redis']);
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        // 查询缓存
        $list = $redis->get('rabbit_rotary_draw');

        // 如果缓存不存在重新写入缓存
        if (empty($list)) {
            $list = Db::name('rabbit_rotary_draw')->order('sort', 'asc')->select()->toArray();
            if (!empty($list)) {
                $list = json_encode($list);
            }
            $redis->set('rabbit_rotary_draw', $list);
        }
        if ($list) {
            $list = json_decode($list, true);
            if (!empty($list)) {
                foreach ($list as &$val) {
                    $val['image'] = env('ALIURL') . $val['image'];
                }
            }
        }
        return $list;
    }

    /**
     * 获取兔头幸运转盘配置
     */
    public function getNewRabbitRotaryDraw()
    {
//        $redis = new Redis(Config('cache')['stores']['redis']);
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        // 查询缓存
        $list = $redis->get('rabbit_rotary_draw_newcomer');

        // 如果缓存不存在重新写入缓存
        if (empty($list)) {
            $list = Db::name('rabbit_rotary_draw_newcomer')->order('sort', 'asc')->select()->toArray();
            if (!empty($list)) {
                $list = json_encode($list);
            }
            $redis->set('rabbit_rotary_draw_newcomer', $list);
        }
        if ($list) {
            $list = json_decode($list, true);
            if (!empty($list)) {
                foreach ($list as &$val) {
                    $val['image'] = env('ALIURL') . $val['image'];
                }
            }
        }
        return $list;
    }

    /**
     * 幸运转盘抽奖
     * @param $param
     * @param $header
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function luckDraw($param, $header)
    {
        // 用户数据
        $userinfo = [];
        if (isset($param['uid']) && !empty($param['uid'])) {
            $userinfo['uid'] = $param['uid'];
            $userinfo['loginname'] = $param['uid'];
        } else {
            $userinfo = $this->checkUserToken($header['securitycheckval']);
        }
        $param['uuid'] = $param['uuid'] ?? '123123';
        // 查询订单状态
        $exists = Db::name('sales_goods_order_lottery_record')
            ->where([
                'uid' => $userinfo['uid'],
                'orderno' => $param['orderno']
            ])->find();
        // 订单检测
        if (!empty($exists)) {
            if ($exists['status'] == 1) {
                return serviceReturn(false, [], '您已抽过');
            }
        } else {
            // 查询最近是否存在未抽奖订单
            $exists = Db::name('sales_goods_order_lottery_record')
                ->where([
                    'uid' => $userinfo['uid'],
                    'status' => 0
                ])->find();
            if (empty($exists)) {
                return serviceReturn(false, [], '抽奖无效，抽奖次数已消耗完毕');
            }
        }

        //获取奖项信息
        $prize_arr = Db::name('rotary_draw')->select()->toArray();
        if ($param['uid'] == 709214) {
            $awards = array(2, 4, 8, 6);
            foreach ($prize_arr as &$val) {
                if (in_array($val['id'], $awards)) {
                    $val['probability'] = 1;
                }
            }
        }

        // 是否为黑名单用户
        /**
        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $user_url = $user_url . '?uid=' . $param['uid'] . '&field=payment_lottery_blacklist';
        $user_list = get_url($user_url);
        $user_list = get_interior_http_response($user_list);
        $user_list = $user_list['list'] ?? [];
        if (!empty($user_list)) {
            if ($user_list[0]['payment_lottery_blacklist']) {
                // 黑名单奖项
                $prize_arr = Db::name('rotary_draw_blacklist')->select()->toArray();
            }
        } */


        $resnum = array();
        foreach ($prize_arr as $k => &$v) {
            $v['price'] = 0;
            $arr['probability'] = $v['probability'];//中奖比率
            $arr['id'] = $v['id'];//奖项ID
            for ($i = 0; $i < $v['probability']; $i++) {
                $resnum[] = $v['id'];
            }
            if ($v['type'] == 1) {
                $v['price'] = (int)$v['number'];
                $v['data_id'] = 0;
            } elseif ($v['type'] == 2) {
                $v['data_id'] = $v['number'];
                $v['price'] = 0;
            }
            $v['title'] = $v['name'];
            if ($v['id'] == 1) {
                $v['msg'] = '非常遗憾!未中奖';
            } else {
                $v['msg'] = '恭喜您，获得' . $v['name'];
            }
        }

        $prize_key = array_rand($resnum, 1);//获取奖项

        $prize_id = $resnum[$prize_key];
        $prizenew = array_column($prize_arr, null, 'id');
        $data = $prizenew[$prize_id];//获取中奖信息

        $returnInfo = $this->InsertWinningRecord($data, $userinfo, $header, $param['orderno']);
        $info['period'] = 0;
        if(isset($returnInfo['coupon_type'])){
            if(in_array($returnInfo['coupon_type'],[1007,1008])){
                $relation_id_arr = explode(',',$returnInfo['relation_id']);
                $info['period'] = isset($relation_id_arr[0])?$relation_id_arr[0]:0;
            }
        }
        // 改变抽奖状态
        $t = time();
        Db::name('sales_goods_order_lottery_record')
            ->where([
                'id' => $exists['id'],
            ])->update(['status' => 1, 'update_time' => $t]);
        $info['id'] = $data['id'];
        $info['msg'] = $data['msg'];
        $info['type'] = $data['type'];

        $msg['flag'] = 1;
        $msg['msg'] = '执行成功';
        $msg['data'] = $info;

        return serviceReturn(true, $msg);


    }

    /**
     * 发放中奖奖励及记录
     * @param $data
     * @param $userinfo
     * @param $header
     * @param $orderno
     * @return bool|int|mixed
     */
    public function InsertWinningRecord($data, $userinfo, $header, $orderno)
    {
        $uid = $userinfo['uid'];//用户id
        //中奖记录
        $info = array(
            'uid' => $uid,//用户id
            'type' => $data['type'],//中奖类型 0未中奖 1兔头 2 代金券 3 酒
            'prize' => (int)$data['price'],//奖项金额
            'order_id' => $orderno, // 订单号
            'award' => $data['title'],//奖项名称
            'remark' => $data['msg'],//备注
            'addtime' => date('Y-m-d H:i:s', time()),//中奖时间
        );

        //如果用户抽中优惠券,自动发放优惠券
        if ($data['type'] == 2) {
            $body = [
                'uid' => $uid,
                'phone' => $userinfo['loginname'],
            ];

            $param['user_info'] = $body;
            $param['couple_id'] = $data['data_id'];

            // 优惠券可能有多个
            $couple_ids = explode(',', $data['data_id']);
            if (sizeof($couple_ids) == 1) {
                // 只配一个优惠券直接取
                $data['data_id'] = intval($couple_ids[0]);
            } else {
                /**
                $goods_ids = [];
                // 查询优惠券
                $couple_list = Db::name('coupon')->field('id, coupon_type, relation_id')->where('id', 'in', $couple_ids)->orderRaw('field(id, '.$data['data_id'].')')->select();
                foreach ($couple_list as $key => $val) {
                    // 指定商品的优惠券的情况
                    if (in_array($val['coupon_type'], [1007, 1008])) {
                        // 筛选出期数
                        $goods_ids[] = $val['relation_id'];
                    }
                }

                // 查询es商品信息
                $goods_ids_str = $goods_ids ? implode(',', $goods_ids) : '';
                $es_goods = AdExternal::getGoodListByids($goods_ids_str);
                $_goods_list = $es_goods['list'];
                $goods_list = array_column($_goods_list, null, 'id');

                // 查询优惠券发放记录
                $_couple_record_list = Db::name('coupon_issue')->field('coupon_id, count(1) as record_num')
                    ->where([
                        ['coupon_id', 'in', $couple_ids],
                        ['expire_time', '>', time()]
                    ])
                    ->group('coupon_id')
                    ->select()
                    ->toArray();
                $couple_record_list = array_column($_couple_record_list, null, 'coupon_id');

                $coupon_id = 0;
                foreach ($couple_list as $key => $val) {
                    if (!in_array($val['coupon_type'], [1007, 1008])) {
                        $coupon_id = $val['id'];
                        break;
                    } else {
                        if (isset($goods_list[$val['relation_id']])) {
                            $goods = $goods_list[$val['relation_id']];
                        } else {
                            break;
                        }
                        if (isset($couple_record_list[$val['id']])) {
                            $couple_record = $couple_record_list[$val['id']];
                        } else {
                            if ($goods['inventory'] > 0 && $goods['onsale_status'] == 2) {
                                $coupon_id = $val['id'];
                            }
                            break;
                        }
                        if ($goods['inventory'] > 0 && $goods['onsale_status'] == 2 && $couple_record['record_num'] < $goods['inventory']) {
                            $coupon_id = $val['id'];
                            break;
                        }
                    }
                }

                // 如果都不符合条件 取最后一个
                if ($coupon_id == 0) {
                    $coupon_id = $couple_ids[sizeof($couple_ids) - 1];
                }
                $data['data_id'] = intval($coupon_id);
                 */
                // 随机三次取一个优惠券
                $couple_id = 0;
                for ($i = 1; $i <= 3; $i++) {
                    $rand_id = array_rand($couple_ids);
                    $couple_id = $couple_ids[$rand_id];
                    $couple = Db::name('coupon')->field('id, coupon_type, relation_id')
                        ->where('id', '=', $couple_id)
                        ->find();
                    $goods_id = $couple['relation_id'];
                    $es_goods = AdExternal::getGoodListByids($goods_id);
                    $_goods_list = $es_goods['list'];
                    $goods = $_goods_list[0];
                    if ($goods['inventory'] > 0 && $goods['onsale_status'] == 2) {
                        $data['data_id'] = $couple_id;
                        break;
                    } else {
                        // 清除此优惠券
                        if (sizeof($couple_ids) > 1) {
                            unset($couple_ids[$rand_id]);
                            $s_c_a = implode(',', $couple_ids);
                            $this->updateRotaryDraw([
                                'id' => $data['id'],
                                'number' => $s_c_a,
                                'updated_time' => time()
                            ]);
                        }
                        $couple_id = 0;
                    }
                }
                // 如果都不符合条件 取最后一个
                if ($couple_id == 0) {
                    $data['data_id'] = end($couple_ids);
                    $couple_ids = reset($couple_ids);
                }
            }
            $data['data_id'] = intval($data['data_id']);
            // 推送队列
            $queue_data['platform'] = 'v3';
            $queue_data['type'] = 'coupon';
            $queue_data['issue_num'] = 1;
            $queue_data['coupon_id'] = [$data['data_id']];
            $queue_data['callback'] = '';
            $queue_data['uid'] = (int)$uid;
            $queue_data['telephone'] = $userinfo['loginname'];
            $queue_data['activity_name'] = '支付幸运大转盘抽奖';
            $q_data = base64_encode(json_encode($queue_data));
            // 请求头
            $url_header[] = 'vinehoo-client: tp6-marketing-conf';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $q['exchange_name'] = 'activity';
            $q['routing_key'] = 'activity.coupon.rabbits';
            $q['data'] = $q_data;
            // 推送
            $result_re = post_url(env("ITEM.QUEUE_URL"), json_encode($q), $url_header);
//            $this->pushCoupon($param, $header);
            Db::name('share_draw')->insert($info);
            //获取优惠券指定期数处理
            $couponInfo = Db::name('coupon')->field('coupon_type,relation_id')->where(['id'=>$data['data_id']])->find();
            return $couponInfo;
        }

        //更新用户兔头
        if ($data['type'] == 1) {
            $queue_data['platform'] = 'v3';
            $queue_data['type'] = 'rabbit';
            $queue_data['rabbits'] = (int)$data['price'];
            $queue_data['uid'] = (int)$uid;
            $queue_data['telephone'] = $userinfo['loginname'];
            $queue_data['callback'] = '';
            $queue_data['activity_name'] = '幸运大转盘抽奖~';
            $queue_data['reason'] = '兔头幸运大转盘抽奖~';
            $q_data = base64_encode(json_encode($queue_data));

            // 请求头
            $url_header[] = 'vinehoo-client: tp6-marketing-conf';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
//            $url_header[] = 'Content-Length: ' . strlen($q_data);
            $q['exchange_name'] = 'activity';
            $q['routing_key'] = 'activity.coupon.rabbits';
            $q['data'] = $q_data;
            // 推送
            $result_re = post_url(env("ITEM.QUEUE_URL"), json_encode($q), $url_header);
//            $this->updateRabbit($header, $uid, $nowrabbit);
        }

        return Db::name('share_draw')->insert($info);
    }


    /**
     * 添加订单购买记录
     * @param array $params
     * @return int|string
     */
    public function salesOrderRecord(array $params)
    {
        return Db::name('sales_goods_order_lottery_record')->insert($params);
    }

    /**
     * 兔头抽奖
     * @param $param
     * @param $header
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rabbitLuckDraw($param, $header)
    {
        // 用户数据
        $userinfo = [];
        if (isset($param['uid']) && !empty($param['uid'])) {
            $userinfo['uid'] = $param['uid'];
            $userinfo['loginname'] = $param['uname'];
        }

        //获取奖项信息
        $prize_arr = Db::name('rabbit_rotary_draw')->select()->toArray();

        $resnum = array();
        foreach ($prize_arr as $k => &$v) {
            $v['price'] = 0;
            $arr['probability'] = $v['probability'];//中奖比率
            $arr['id'] = $v['id'];//奖项ID
            for ($i = 0; $i < $v['probability']; $i++) {
                $resnum[] = $v['id'];
            }
            if ($v['type'] == 1) {
                $v['price'] = (int)$v['number'];
                $v['data_id'] = 0;
            } elseif ($v['type'] == 2) {
                $v['data_id'] = $v['number'];
                $v['price'] = 0;
            }
            $v['title'] = $v['name'];
            if ($v['id'] == 1) {
                $v['msg'] = '非常遗憾!未中奖';
            } else {
                $v['msg'] = '恭喜您，获得' . $v['name'];
            }
        }

        $prize_key = array_rand($resnum, 1);//获取奖项

        $prize_id = $resnum[$prize_key];
        $prizenew = array_column($prize_arr, null, 'id');
        $data = $prizenew[$prize_id];//获取中奖信息

        // 先扣兔头再发送奖品
        if ($param['is_rabbit'] == 1) {
            // 扣除兔头
            $user_url = env('ITEM.USER_URL') . '/user/v3/user/updateAssets';
            $assets_data['uid'] = $param['uid'];
            $assets_data['change_type'] = 2;
            $assets_data['rabbit'] = 10;
            $assets_data['source'] = 2;
            $assets_data['reason'] = '兔头幸运抽奖';
            $assets_data_json = json_encode($assets_data);
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $user_assets = post_url($user_url, $assets_data_json, $url_header);
            $user_assets = json_decode($user_assets, true);
            if ($user_assets['error_code'] != 0) {
                return serviceReturn(false, $user_assets['error_msg']);
            }
        }

        // 发送奖品
        $ins_ret = $this->rabbitInsertWinningRecord($data, $userinfo);

        // 改变抽奖状态
        $info['id'] = $data['id'];
        $info['msg'] = $data['msg'];
        $info['type'] = $data['type'];
        if (!empty($ins_ret['assign_info'])) {
            $info = array_merge($info, $ins_ret['assign_info']);
        }

        $msg['flag'] = 1;
        $msg['msg'] = '执行成功';
        $msg['data'] = $info;

        return serviceReturn(true, $msg);

    }

    /**
     * 兔头抽奖奖品兑换
     * @param $data
     * @param $userinfo +
     * @return bool|int|mixed
     */
    public function rabbitInsertWinningRecord($data, $userinfo)
    {
        $uid = $userinfo['uid'];//用户id
        //中奖记录
        $info = array(
            'uid' => $uid,//用户id
            'type' => $data['type'],//中奖类型 0未中奖 1兔头 2 代金券 3 酒
            'prize' => (int)$data['price'],//奖项金额
            'award' => $data['name'],//奖项名称
            'remark' => $data['msg'],//备注
            'addtime' => date('Y-m-d H:i:s', time()),//中奖时间
        );
        //如果用户抽中优惠券,自动发放优惠券
        if ($data['type'] == 2) {
            $body = [
                'uid' => $uid,
                'phone' => $userinfo['loginname'],
            ];

            $param['user_info'] = $body;
            $param['couple_id'] = $data['data_id'];

            // 优惠券可能有多个
            $couple_ids = explode(',', $data['data_id']);
            if (false !== strpos($data['name'], '指定')) {
                $end_coupon_id = end($couple_ids);

                $couples = Db::name('coupon')
                    ->where('id', 'in', $couple_ids)
                    ->column('relation_id', 'id');

                $couples_group = [];
                foreach ($couples as $couple_id => $relation_item_id) {
                    $relation_item_id_arr = explode(',',$relation_item_id);
                    foreach ($relation_item_id_arr as $relation_item_id_item){
                        $couples_group[$relation_item_id_item][] = $couple_id;
                    }
                }

                $goods_list = Es::name(Es::PERIODS)->where([
                    ['id', 'in', array_values($couples)],
                    ['inventory', '>', 0],
                    ['onsale_status', '=', 2],
                ])->field('id,inventory,onsale_status')->select()->toArray();

                $valid_goods_ids = array_column($goods_list, 'id');

                $valid_coupon_ids = [];
                foreach ($valid_goods_ids as $valid_goods_id) {
                    $valid_coupon_ids = array_merge($valid_coupon_ids, $couples_group[$valid_goods_id]);
                }

                $valid_coupon_ids[] = $end_coupon_id;
                $valid_coupon_ids   = array_values(array_unique($valid_coupon_ids));

                if (count($valid_coupon_ids) != count($couple_ids)) {
                    $this->rabbitUpdateRotaryDraw([
                        'id'           => $data['id'],
                        'number'       => implode(',', $valid_coupon_ids),
                        'updated_time' => time()
                    ]);
                }
                $data['data_id'] = $valid_coupon_ids[array_rand($valid_coupon_ids)];

                $assign_info = [
                    'coupon_id' => $data['data_id'],
                    'first_goods_id' => (explode(',', ($couples[$data['data_id']] ?? ''))[0] ?? '')
                ];
            }
//            region 注释掉的内容
//            if (sizeof($couple_ids) == 1) {
//                // 只配一个优惠券直接取
//                $data['data_id'] = intval($couple_ids[0]);
//            } else {
//                $goods_ids = [];
//                // 查询优惠券
//                $couple_list = Db::name('coupon')->field('id, coupon_type, relation_id')->where('id', 'in', $couple_ids)->orderRaw('field(id, '.$data['data_id'].')')->select();
//                foreach ($couple_list as $key => $val) {
//                    // 指定商品的优惠券的情况
//                    if (in_array($val['coupon_type'], [1007, 1008])) {
//                        // 筛选出期数
//                        $goods_ids[] = $val['relation_id'];
//                    }
//                }
//
//                // 查询es商品信息
//                $goods_ids_str = $goods_ids ? implode(',', $goods_ids) : '';
//                $es_goods = AdExternal::getGoodListByids($goods_ids_str);
//                $_goods_list = $es_goods['list'];
//                $goods_list = array_column($_goods_list, null, 'id');
//
//                // 查询优惠券发放记录
//                $_couple_record_list = Db::name('coupon_issue')->field('coupon_id, count(1) as record_num')->where('coupon_id', 'in', $couple_ids)->group('coupon_id')->select()->toArray();
//                $couple_record_list = array_column($_couple_record_list, null, 'coupon_id');
//
//                $coupon_id = 0;
//                //如果是多个优惠卷将数据取其中一个   前提是要库存没超过
//                $availableCouponIds = [];
//                foreach ($couple_list as $key => $val) {
//                    /*if (!in_array($val['coupon_type'], [1007, 1008])) {
//                        $coupon_id = $val['id'];
//                        break;
//                    } else {
//
//                    }*/
//                    if (isset($goods_list[$val['relation_id']])) {
//                        $goods = $goods_list[$val['relation_id']];
//                    } else {
//                        //没有商品信息跳过
//                        //可发放的优惠卷
//                        array_push($availableCouponIds,$val['id']);
//                        continue;
//                    }
//                    if (isset($couple_record_list[$val['id']])) {
//                        $couple_record = $couple_record_list[$val['id']];
//                    } else {
//                        /*$coupon_id = $val['id'];
//                        break;*/
//                        //无发放记录 生成已发放数量为0
//                        $couple_record = ['record_num' => 0];
//                    }
//                    if ($goods['inventory'] > 0 && $goods['onsale_status'] == 2 && $couple_record['record_num'] < $goods['inventory']) {
//                        $coupon_id = $val['id'];
//                        //可发放的优惠卷
//                        array_push($availableCouponIds,$coupon_id);
//                    }
//                }
//
//                //有多个可发放优惠卷时随机取其中一个
//                if(count($availableCouponIds) > 0){
//                    $coupon_id = $availableCouponIds[array_rand($availableCouponIds)];
//                }
//
//                // 如果都不符合条件 取最后一个
//                if ($coupon_id == 0) {
//                    $coupon_id = $couple_ids[sizeof($couple_ids) - 1];
//                }
//                $data['data_id'] = intval($coupon_id);
//            }
            #endregion

            // 推送队列
            $queue_data['platform'] = 'v3';
            $queue_data['type'] = 'coupon';
            $queue_data['issue_num'] = 1;
            $queue_data['coupon_id'] = [intval($data['data_id'])];
            $queue_data['callback'] = '';
            $queue_data['uid'] = (int)$uid;
            $queue_data['telephone'] = $userinfo['loginname'];
            $queue_data['activity_name'] = '兔头幸运大转盘抽奖';
            $q_data = base64_encode(json_encode($queue_data));
            // 请求头
            $url_header[] = 'vinehoo-client: tp6-marketing-conf';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $q['exchange_name'] = 'activity';
            $q['routing_key'] = 'activity.coupon.rabbits';
            $q['data'] = $q_data;
            // 推送
            post_url(env("ITEM.QUEUE_URL"), json_encode($q), $url_header);
//            $this->pushCoupon($param, $header);
            $ret['res'] = Db::name('rabbit_share_draw')->insert($info);
            if (isset($assign_info)) {
                $ret['assign_info'] = $assign_info;
            }
            return $ret;
        }

        //更新用户兔头
        if ($data['type'] == 1) {
            $queue_data['platform'] = 'v3';
            $queue_data['type'] = 'rabbit';
            $queue_data['rabbits'] = (int)$data['price'];
            $queue_data['uid'] = (int)$uid;
            $queue_data['telephone'] = $userinfo['loginname'];
            $queue_data['callback'] = '';
            $queue_data['activity_name'] = '幸运大转盘抽奖';
            $queue_data['reason'] = '兔头幸运大转盘抽奖';
            $q_data = base64_encode(json_encode($queue_data));

            // 请求头
            $url_header[] = 'vinehoo-client: tp6-marketing-conf';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
//            $url_header[] = 'Content-Length: ' . strlen($q_data);
            $q['exchange_name'] = 'activity';
            $q['routing_key'] = 'activity.coupon.rabbits';
            $q['data'] = $q_data;
            // 推送
            post_url(env("ITEM.QUEUE_URL"), json_encode($q), $url_header);
//            $this->updateRabbit($header, $uid, $nowrabbit);
        }

        // 添加中奖记录
        $ret['res'] = Db::name('rabbit_share_draw')->insert($info);
        if (isset($assign_info)) {
            $ret['assign_info'] = $assign_info;
        }
        return $ret;
    }


    /**
     * 兔头抽奖中奖记录
     * @param $uid
     * @param int $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function rabbitShareDrawList(int $uid, $limit = 10): array
    {
        $list = Db::name('rabbit_share_draw')
            ->field('id,uid,award, remark,addtime')
            ->order('addtime', 'desc')
            ->where('uid', $uid)->paginate([
            'list_rows' => $limit,
            'var_page' => 'page',
        ]);
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];
        return $result;
    }

    /**
     * 兔头抽奖中奖记录
     * @param $uid
     * @param int $params
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function newRabbitShareDrawList(int $uid, $limit = 10): array
    {
        $list = Db::name('rabbit_share_draw_newcomer')
            ->field('id,uid,award, remark,addtime')
            ->order('addtime', 'desc')
            ->where('uid', $uid)->paginate([
                'list_rows' => $limit,
                'var_page' => 'page',
            ]);
        $result['total'] = $list->total() ?? 0;
        $result['list'] = $list->getCollection()->toArray() ?? [];
        return $result;
    }

    /**
     * 查询用户抽奖信息
     * @param int $uid
     * @return array
     */
    public function getUserLotteryInfo(int $uid): array
    {
        // 获取用户兔头
        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $user_url = $user_url . '?uid=' . $uid . '&field=uid,rabbit';
        $user_list = get_url($user_url);
        $user_list = get_interior_http_response($user_list);
        $user_list = $user_list['list'] ?? [];
        $user_rabbit = $user_list[0]['rabbit'] ?? 0;
        // 获取用户今日可抽次数，每日可免费抽一次
        $start = date('Y-m-d 23:59:59', strtotime("-1 day", time()));
        $end_time = date('Y-m-d 23:59:39', time());
        $lottery_count = Db::name('rabbit_share_draw')
            ->where([['addtime', '>', $start],['addtime', '<', $end_time]])
            ->where('uid', $uid)
            ->count();
        $usable_count = 1;
        if ($lottery_count > 0) {
            $usable_count = 0;
        }
        // 抽奖总数
        $share_draw_count = Db::name('rabbit_share_draw')
            ->field('id')
            ->where('uid', $uid)
            ->count();
        $data['rabbit'] = $user_rabbit;
        $data['usable_count'] = $usable_count;
        $data['share_draw_count'] = $share_draw_count;
        return $data;
    }

    /**
     * 查询新人用户抽奖信息
     * @param int $uid
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function getNewcomerLotteryInfo(int $uid): array
    {
        // 获取用户兔头
        $user_url = env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo';
        $user_url = $user_url . '?uid=' . $uid . '&field=uid,rabbit';
        $user_list = get_url($user_url);
        $user_list = get_interior_http_response($user_list);
        $user_list = $user_list['list'] ?? [];
        $user_rabbit = $user_list[0]['rabbit'] ?? 0;
        // 获取用户今日可抽次数，每日可免费抽一次
        $start = date('Y-m-d 23:59:59', strtotime("-1 day", time()));
        $end_time = date('Y-m-d 23:59:39', time());
        $lottery_count = Db::name('rabbit_share_draw_newcomer')
            ->where([['addtime', '>', $start],['addtime', '<', $end_time]])
            ->where('uid', $uid)
            ->count();
        $usable_count = 1;
        if ($lottery_count > 0) {
            $usable_count = 0;
        }
        $data['usable_count'] = $usable_count;

        // 抽奖总数
        $share_draw_count = Db::name('rabbit_share_draw_newcomer')
            ->field('id')
            ->where('uid', $uid)
            ->count();
        // 分享抽奖次数
        // $start = strtotime(date('Y-m-d 23:59:59', strtotime("-1 day", time())));
        // $end_time = strtotime(date('Y-m-d 23:59:39', time()));
        $share = Db::name('rabbit_share_count_newcomer')
            ->field('sum(share_count) share_num,sum(use_count) as use_num')
            // ->where([['created_time', '>', $start],['created_time', '<', $end_time]])
            ->where('uid', $uid)
            ->find();
        $data['share_num'] = 0;
        $data['use_num'] = 0;
        if (!empty($share)) {
            $data['share_num'] = $share['share_num'];
            $data['use_num'] = $share['use_num'];
            $data['usable_count'] += $share['share_num'] - $share['use_num'];
        }
        $data['rabbit'] = $user_rabbit;
        $data['share_draw_count'] = $share_draw_count;
        return $data;
    }

    /**
     * 查询订单号抽奖状态
     * @param string $orderno
     * @return array|mixed|Db|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUserOrderStatus(string $orderno) {
        return Db::name('sales_goods_order_lottery_record')->where('orderno', $orderno)->find();
    }

    /**
     * 查询用户剩余抽奖次数
     * @param int $uid
     * @return int
     * @throws \think\db\exception\DbException
     */
    public function getUserOrderCount(int $uid): int
    {
        return Db::name('sales_goods_order_lottery_record')->where(['uid' => $uid, 'status' => 0])->count();

    }

    /**
     * 兔头抽奖
     * @param $param
     * @param $header
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function newRabbitLuckDraw($param, $header)
    {
        // 用户数据
        $userinfo = [];
        if (isset($param['uid']) && !empty($param['uid'])) {
            $userinfo['uid'] = $param['uid'];
            $userinfo['loginname'] = $param['uname'];
        }
        $where = [];
        //老用户
        if (empty($param['is_new_user']) || intval($param['is_new_user']) !== 1) {
            $where = [
                ['target_user', '=', 0],
            ];
        }
        //获取奖项信息
        $prize_arr = Db::name('rabbit_rotary_draw_newcomer')->where($where)->select()->toArray();

        $resnum = array();
        foreach ($prize_arr as $k => &$v) {
            $v['price'] = 0;
            $arr['probability'] = $v['probability'];//中奖比率
            $arr['id'] = $v['id'];//奖项ID
            for ($i = 0; $i < $v['probability']; $i++) {
                $resnum[] = $v['id'];
            }
            $v['msg'] = '恭喜您！获得' . $v['name'];
            switch ($v['type']) {
                case 0://未中奖
                    $v['msg'] = '非常遗憾!未中奖';
                    break;
                case 1://兔头
                    $v['price'] = (int)$v['number'];
                    $v['data_id'] = 0;
                    break;
                case 2://优惠券
                    $v['data_id'] = $v['number'];
                    $v['price'] = 0;
                    break;
            }
        }

        $prize_key = array_rand($resnum, 1);//获取奖项

        $prize_id = $resnum[$prize_key];
        $prizenew = array_column($prize_arr, null, 'id');
        $data = $prizenew[$prize_id];//获取中奖信息

        // 先扣兔头再发送奖品
        if ($param['is_rabbit'] == 1) {
            // 扣除兔头
            $user_url = env('ITEM.USER_URL') . '/user/v3/user/updateAssets';
            $assets_data['uid'] = $param['uid'];
            $assets_data['change_type'] = 2;
            $assets_data['rabbit'] = 10;
            $assets_data['source'] = 2;
            $assets_data['reason'] = '兔头幸运抽奖';
            $assets_data_json = json_encode($assets_data);
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $user_assets = post_url($user_url, $assets_data_json, $url_header);
            $user_assets = json_decode($user_assets, true);
            if ($user_assets['error_code'] != 0) {
                return serviceReturn(false, $user_assets['error_msg']);
            }
        }

        // 发送奖品
        $this->newRabbitInsertWinningRecord($data, $userinfo);

        // 改变抽奖状态
        $info['id'] = $data['id'];
        $info['msg'] = $data['msg'];
        $info['type'] = $data['type'];
        $info['prize'] = $data['number'];

        $msg['flag'] = 1;
        $msg['msg'] = '执行成功';
        $msg['data'] = $info;

        return serviceReturn(true, $msg);

    }

    /**
     * 兔头抽奖奖品兑换
     * @param $data
     * @param $userinfo +
     * @return bool|int|mixed
     */
    public function newRabbitInsertWinningRecord($data, $userinfo)
    {
        $uid = $userinfo['uid'];//用户id
        //中奖记录
        $info = array(
            'uid' => $uid,//用户id
            'type' => $data['type'],//中奖类型 0未中奖 1兔头 2 代金券 3 酒
            'prize' => (int)$data['price'],//奖项金额
            'award' => $data['name'],//奖项名称
            'remark' => $data['msg'],//备注
            'addtime' => date('Y-m-d H:i:s', time()),//中奖时间
        );
        //如果用户抽中优惠券,自动发放优惠券
        if ($data['type'] == 2) {
            $body = [
                'uid' => $uid,
                'phone' => $userinfo['loginname'],
            ];

            $param['user_info'] = $body;
            $param['couple_id'] = $data['data_id'];

            // 优惠券可能有多个
            $couple_ids = explode(',', $data['data_id']);
            if (sizeof($couple_ids) == 1) {
                // 只配一个优惠券直接取
                $data['data_id'] = intval($couple_ids[0]);
            } else {
                $goods_ids = [];
                // 查询优惠券
                $couple_list = Db::name('coupon')->field('id, coupon_type, relation_id')->where('id', 'in', $couple_ids)->orderRaw('field(id, '.$data['data_id'].')')->select();
                foreach ($couple_list as $key => $val) {
                    // 指定商品的优惠券的情况
                    if (in_array($val['coupon_type'], [1007, 1008])) {
                        // 筛选出期数
                        $goods_ids[] = $val['relation_id'];
                    }
                }

                // 查询es商品信息
                $goods_ids_str = $goods_ids ? implode(',', $goods_ids) : '';
                $es_goods = AdExternal::getGoodListByids($goods_ids_str);
                $_goods_list = $es_goods['list'];
                $goods_list = array_column($_goods_list, null, 'id');

                // 查询优惠券发放记录
                $_couple_record_list = Db::name('coupon_issue')->field('coupon_id, count(1) as record_num')->where('coupon_id', 'in', $couple_ids)->group('coupon_id')->select()->toArray();
                $couple_record_list = array_column($_couple_record_list, null, 'coupon_id');

                $coupon_id = 0;
                //如果是多个优惠卷将数据取其中一个   前提是要库存没超过
                $availableCouponIds = [];
                foreach ($couple_list as $key => $val) {
                    /*if (!in_array($val['coupon_type'], [1007, 1008])) {
                        $coupon_id = $val['id'];
                        break;
                    } else {

                    }*/
                    if (isset($goods_list[$val['relation_id']])) {
                        $goods = $goods_list[$val['relation_id']];
                    } else {
                        //没有商品信息跳过
                        //可发放的优惠卷
                        array_push($availableCouponIds,$val['id']);
                        continue;
                    }
                    if (isset($couple_record_list[$val['id']])) {
                        $couple_record = $couple_record_list[$val['id']];
                    } else {
                        /*$coupon_id = $val['id'];
                        break;*/
                        //无发放记录 生成已发放数量为0
                        $couple_record = ['record_num' => 0];
                    }
                    if ($goods['inventory'] > 0 && $goods['onsale_status'] == 2 && $couple_record['record_num'] < $goods['inventory']) {
                        $coupon_id = $val['id'];
                        //可发放的优惠卷
                        array_push($availableCouponIds,$coupon_id);
                    }
                }

                //有多个可发放优惠卷时随机取其中一个
                if(count($availableCouponIds) > 0){
                    $coupon_id = $availableCouponIds[array_rand($availableCouponIds)];
                }

                // 如果都不符合条件 取最后一个
                if ($coupon_id == 0) {
                    $coupon_id = $couple_ids[sizeof($couple_ids) - 1];
                }
                $data['data_id'] = intval($coupon_id);
            }

            // 推送队列
            $queue_data['platform'] = 'v3';
            $queue_data['type'] = 'coupon';
            $queue_data['issue_num'] = 1;
            $queue_data['coupon_id'] = [$data['data_id']];
            $queue_data['callback'] = '';
            $queue_data['uid'] = (int)$uid;
            $queue_data['telephone'] = $userinfo['loginname'];
            $queue_data['activity_name'] = '新人兔头幸运大转盘抽奖';
            $q_data = base64_encode(json_encode($queue_data));
            // 请求头
            $url_header[] = 'vinehoo-client: tp6-marketing-conf';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
            $q['exchange_name'] = 'activity';
            $q['routing_key'] = 'activity.coupon.rabbits';
            $q['data'] = $q_data;
            // 推送
            post_url(env("ITEM.QUEUE_URL"), json_encode($q), $url_header);
//            $this->pushCoupon($param, $header);
            return Db::name('rabbit_share_draw_newcomer')->insert($info);
        }

        //更新用户兔头
        if ($data['type'] == 1) {
            $queue_data['platform'] = 'v3';
            $queue_data['type'] = 'rabbit';
            $queue_data['rabbits'] = (int)$data['price'];
            $queue_data['uid'] = (int)$uid;
            $queue_data['telephone'] = $userinfo['loginname'];
            $queue_data['callback'] = '';
            $queue_data['activity_name'] = '新人兔头幸运大转盘抽奖';
            $queue_data['reason'] = '新人兔头幸运大转盘抽奖';
            $q_data = base64_encode(json_encode($queue_data));

            // 请求头
            $url_header[] = 'vinehoo-client: tp6-marketing-conf';
            $url_header[] = 'Content-Type: application/json;charset=utf-8';
//            $url_header[] = 'Content-Length: ' . strlen($q_data);
            $q['exchange_name'] = 'activity';
            $q['routing_key'] = 'activity.coupon.rabbits';
            $q['data'] = $q_data;
            // 推送
            post_url(env("ITEM.QUEUE_URL"), json_encode($q), $url_header);
//            $this->updateRabbit($header, $uid, $nowrabbit);
        }

        // 添加中奖记录
        return Db::name('rabbit_share_draw_newcomer')->insert($info);
    }

    /**
     * 分享增加抽奖次数
     * @param $uid
     * @return true
     * @throws \think\db\exception\DbException
     */
    public function newcomerShare($uid): bool
    {
        $start = strtotime(date('Y-m-d 23:59:59', strtotime("-1 day", time())));
        $end_time = strtotime(date('Y-m-d 23:59:39', time()));
        Db::instance();
        $share_count = Db::name('rabbit_share_count_newcomer')
            ->where([['created_time', '>', $start],['created_time', '<', $end_time]])
            ->where('uid', $uid)
            ->lock(true)
            ->value('share_count');
        if (empty($share_count)) {
            $ins = Db::name('rabbit_share_count_newcomer')->insert([
                'uid' => $uid,
                'share_count' => 1,
                'created_time' => time(),
            ]);
        } elseif ($share_count < 5) {
            $inc = Db::name('rabbit_share_count_newcomer')
                ->where('uid', $uid)
                ->where([['created_time', '>', $start],['created_time', '<', $end_time]])
                ->inc('share_count')
                ->update();
        }
        Db::commit();
        return true;
    }

    /**
     * 查询用户分享抽奖次数
     * @param $uid
     * @return array|mixed|Db|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getNewcomerShare($uid)
    {
        // $start = strtotime(date('Y-m-d 23:59:59', strtotime("-1 day", time())));
        // $end_time = strtotime(date('Y-m-d 23:59:39', time()));
        $share = Db::name('rabbit_share_count_newcomer')
            ->field('(sum(share_count)-sum(use_count)) as num')
            // ->where([['created_time', '>', $start],['created_time', '<', $end_time]])
            ->where('uid', $uid)
            ->findOrEmpty();
        if (!empty($share['num'])) {
            return $share['num'] > 0 ? $share['num'] : 0;
        }
        return 0;
    }

    /**
     * 减少分享抽奖次数
     * @param $uid
     * @return true
     * @throws \think\db\exception\DbException
     */
    public function decNewcomerShare($uid)
    {
        // $start = strtotime(date('Y-m-d 23:59:59', strtotime("-1 day", time())));
        // $end_time = strtotime(date('Y-m-d 23:59:39', time()));
        Db::instance();
        try {
            $info = Db::name('rabbit_share_count_newcomer')
                // ->where([['created_time', '>', $start],['created_time', '<', $end_time]])
                ->field('id')
                ->where('uid', $uid)
                ->where('share_count > use_count')
                ->order(['id' => 'asc'])
                ->lock(true)
                ->select()->toArray();
            if (empty($info[0]['id'])) {
                throw new \Exception("抽奖次数获取失败，请稍后再试");
            }
            //扣减次数
            Db::name('rabbit_share_count_newcomer')
                ->where('id', $info[0]['id'])
                ->inc('use_count')
                ->update(['updated_time' => time()]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            #回滚事务
            Db::rollback();
            return $e->getMessage();
        }

    }

    /**
     * 获取当前用户分享次数
     * @param $uid
     * @return int
     * @throws DbException
     */
    public function getNewcomerUseDraw($uid): int
    {
        $start = date('Y-m-d 23:59:59', strtotime("-1 day", time()));
        $end_time = date('Y-m-d 23:59:39', time());
        return Db::name('rabbit_share_draw_newcomer')
            ->where([['addtime', '>', $start],['addtime', '<', $end_time]])
            ->where('uid', $uid)
            ->count();
    }

    /**
     * 新人抽奖更新兔头参数配置
     * @param array $params
     * @return int|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateNewRabbitRotaryDraw(array $params)
    {
        // 更新数据库
        $result = Db::name('rabbit_rotary_draw_newcomer')->where('id', $params['id'])->update($params);
        // 更新失败
        if (!$result) {
            return $result;
        }
        // 更新成功更新缓存
        $list = Db::name('rabbit_rotary_draw_newcomer')->order('sort', 'asc')->select()->toArray();
        if (!empty($list)) {
            $list = json_encode($list);
        }
        $redis = new \Redis();
        $redis->connect(env('cache.HOST'), (int)env('cache.PORT'));
        $redis->auth(env('cache.PASSWORD'));
        $redis->select(1);
        $redis->set('rabbit_rotary_draw_newcomer', $list);
        return $result;
    }

}