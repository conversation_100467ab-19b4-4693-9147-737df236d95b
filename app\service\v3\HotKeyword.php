<?php

namespace app\service\v3;

use app\model\HotKeyword as HotKeywordModel;

class HotKeyword
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
        	if(isset($param['keyword']) && strlen($param['keyword']) > 0) {
	            $query->where('keyword', 'like', '%'. $param['keyword'] . '%');
	        }
            if (isset($param['type']) && strlen($param['type']) > 0) {
                $query->where('type', $param['type']);
            }
        };

        // 列表
        $list = HotKeywordModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'updated_at' => 'desc', 'created_at' => 'desc'],
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);

       /* $checked = [];
        foreach($list as $key => $val) {
            $list[$key]['created_at'] = date('Y-m-d H:i:s', $val['created_at']);
            $list[$key]['updated_at'] = date('Y-m-d H:i:s', $val['updated_at']);

            // 创建人
            $list[$key]['operator_name'] = '';
            if ($val['operator_id'] != 0) {
                $response = json_decode(httpCurl(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info?admin_id=' . $val['operator_id']), true);
                if (!isset($response['error_code']) || $response['error_code'] != 0) {
                    excep($response['error_msg']);
                }

                $list[$key]['operator_name'] = $response['data'][$val['operator_id']]['realname'];
            }
        }*/

        // 总条数
    	$total = HotKeywordModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {
        $param += [
            'updated_at' => time(),
            'created_at' => time()
        ];

        $HotKeywordModel = new HotKeywordModel();
        if (!$HotKeywordModel->save($param)) {
            excep('添加异常');
        }
        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $info = HotKeywordModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('关键词不存在');
        }

//        $param['updated_at'] = time();
        if (!$info->save($param)) {
            excep('编辑异常');
        }
        return true;
    }

    /**
     * 删除
     * @return bool
     */
    static function delete($param) {
        $info = HotKeywordModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('关键词不存在');
        }

        if (!$info->delete()) {
            excep('删除异常');
        }
        return true;
    }

    /**
     * 列表
     * @return array
     */
    static function clientlist($param) {
        $where = function($query) use($param) {
            if(isset($param['keyword']) && strlen($param['keyword']) > 0) {
                $query->where('keyword', 'like', '%'. $param['keyword'] . '%');
            }
            if (isset($param['type']) && strlen($param['type']) > 0) {
                $query->where('type', $param['type']);
            }
            $query->where('is_show', 1);

        };

        // 列表
        $list = HotKeywordModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'updated_at' => 'desc', 'created_at' => 'desc'],
           /* 'page' => $param['page'] ?? 1,
            'limit' => $param['limit'] ?? 100,*/
            'type' => 'select'
        ]);

        /* $checked = [];
         foreach($list as $key => $val) {
             $list[$key]['created_at'] = date('Y-m-d H:i:s', $val['created_at']);
             $list[$key]['updated_at'] = date('Y-m-d H:i:s', $val['updated_at']);

             // 创建人
             $list[$key]['operator_name'] = '';
             if ($val['operator_id'] != 0) {
                 $response = json_decode(httpCurl(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info?admin_id=' . $val['operator_id']), true);
                 if (!isset($response['error_code']) || $response['error_code'] != 0) {
                     excep($response['error_msg']);
                 }

                 $list[$key]['operator_name'] = $response['data'][$val['operator_id']]['realname'];
             }
         }*/

        // 总条数
        $total = HotKeywordModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
            'list' => $list,
            'total' => $total
        ];
    }

}
