<?php

namespace app\service\v3;
use app\model\HoverButton as HoverButtonModel;
use think\facade\Cache;

class HoverButton
{

    /**
     * 列表
     * @param $params ["show":1]
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function list($params)
    {
        $where = function($query) use($params) {
            if (isset($params['show']) && strlen($params['show']) > 0) {
                $query->where('show', $params['show']);
            }
        };
        $order = [
            'show'=>'desc',
            'update_time'=>'desc'
        ];
        $page = $params['page'] ?? 1;$limit = $params['limit'] ?? 10;$pagestart = ($page-1)*$limit;
        $result = HoverButtonModel::field('id,external_action,show,path,room_id,params,start_time,end_time')->where($where)
            ->order($order)->limit($pagestart,$limit)->select();
        $total = HoverButtonModel::where($where)->count();
        return ['list'=>$result,'total'=>$total];
    }

    /**
     * 创建
     * @param $params
     * @return void
     */
    public static function create($params)
    {

        $hovtton = HoverButtonModel::create($params);
        if($params['show'] == 1) self::saveCache($hovtton->id);
    }

    /**
     * 修改
     * @param $params
     * @return void
     */
    public static function update($params)
    {
        $where = ['id'=>$params['id']];
        $HoverButtonModel = HoverButtonModel::where($where)->find();
        if(!$HoverButtonModel) excep("未查找到该数据");
        $HoverButtonModel->update($params);
        if($params['show'] == 1) self::saveCache($params['id']);
        if($params['show'] == 0) {
            $button = Cache::get("REDIS_KEY_HOVER_BUTTON_INDEX");
            $data = json_decode($button,true);
            if($data['data']['id'] == $params['id']){
                self::saveCache($params['id'],2);
            }
        }
    }

    /**
     * 删除
     * @param $params
     * @return bool
     */
    public static function delete($params)
    {
        $showData = HoverButtonModel::whereIn('id',$params['id'])->where('show',1)->find();
        if($showData) self::saveCache($params['id'],2);
        $result = HoverButtonModel::destroy(explode(",",$params['id']));
        return $result;
    }


    /**
     * @param $id
     * @param $from
     * @return bool
     * @throws \app\CustomException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function saveCache($id,$from =1)
    {
        $where = ['id'=>$id];
        $result = HoverButtonModel::field('id,external_action,show,path,room_id,params,start_time,end_time')->where($where)->find();
        if(!$result) excep("未查找到该数据");
        if($from == 2)$result->show = 0;//删除时隐藏按钮

        $data = $result->toArray();
        if (!empty($data['room_id'])) {
            $data['room_id'] = strval($data['room_id']);
        }
        $cacheData = ["error_code"=>0,"error_msg"=>"","data"=>$data];
        $button = Cache::set("REDIS_KEY_HOVER_BUTTON_INDEX",json_encode($cacheData));
        return $button;
    }

    /**
     * 修改状态
     * @param $params
     * @return void
     * @throws \app\CustomException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function showsave($params)
    {
        $where = ['id'=>$params['id']];
        $HoverButtonModel = HoverButtonModel::where($where)->find();
        if(!$HoverButtonModel) excep("未查找到该数据");
        $HoverButtonModel->update($params);
        if($params['show'] == 1) self::saveCache($params['id']);
    }

    /**
     * 验证唯一 开启状态
     * @param $params
     * @return bool true 存在 false 不存在
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function validate($params)
    {
        $where[] = ['show','=',1];
        if(isset($params['id'])){
            $where[] = ['id','<>',$params['id']];
        }
        $HoverButtonModel = HoverButtonModel::where($where)->find();
        if($HoverButtonModel) return true;
        return false;
    }
}