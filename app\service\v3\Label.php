<?php

namespace app\service\v3;

use app\model\Label as LabelModel;
use app\model\LabelPathExtends;
use think\facade\Cache;
use think\Request;

class Label
{
    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', $param['channel']);
            }

            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', $param['status']);
            }

            if (isset($param['page_area']) && strlen($param['page_area']) > 0) {
                $query->where('page_area', $param['page_area']);
            }
        };

        // 列表
        $list = LabelModel::getInf([
            'where' => $where,
            'with' => [
                'client_path' => function($query) {
//                    $query->field('id, path_name');
                    $query->find();
                },
                'ad_path_param' => function($query) {
                    $query->find();
                }
            ],
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);

        $label_channel = config('config.ad_capsule_channel');
        foreach($list as $key => $val) {
            $list[$key]['created_at'] = date('Y-m-d H:i:s', $val['created_at']);
            $list[$key]['icon'] = $val['icon'] ? env('ALIURL') . $val['icon'] : "";
            $list[$key]['badge'] = $val['badge'] ? env('ALIURL') . $val['badge'] : "";

            foreach ($label_channel as $k => $v) {
                if ($val['channel'] == $v['id']) {
                    $list[$key]['channel_name'] = $v['name'];
                }
            }

        }

        // 总条数
    	$total = LabelModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {


        $LabelModel = new LabelModel();
        $id = $LabelModel->strict(false)->insertGetId($param);
        if (!$id) {
            excep('添加异常');
        }
        if(isset($param['path_id'])){
            \app\service\v3\LabelPathExtends::create($id,$param['params'],$param['path_id']);
        }
        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $info = LabelModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('标签不存在');
        }

        if (!$info->save($param)) {
            excep('编辑异常');
        }

        $data = $param['params'];
        foreach ($data as $k=>$v){
            $data[$k]['peid'] = $v['id'];
            $data[$k]['pid'] = $param['path_id'];
        }

        if(isset($param['path_id'])){
            LabelPathExtends::where("lid",$param['id'])->delete();
            \app\service\v3\LabelPathExtends::create($param['id'],$data,$param['path_id']);
        }

        /*foreach ($data as $k=>$v){
            $data[$k]['ios_val']=$v['value'];
            $data[$k]['android_val']=$v['value'];
            $data[$k]['mini_val']=$v['value'];
            $data[$k]['h5_val']=$v['value'];
            unset($data[$k]['value']);

            $obj = new  \app\model\LabelPathExtends();

            $obj->where("id",$v['id'])->update($data[$k]);

            if(isset($param['path_id'])){
                \app\service\v3\LabelPathExtends::create($param['id'],$data[$k],$param['path_id']);
            }

        }*/
        return true;
    }

    static function clientlist($param){

        #region 根据版本号返回内容 @cct
        //允许的查询参数
        $clients  = [0, 1]; //客户端 0:ios 1:安卓 2:小程序 3:h5
        $channels = [0, 1, 2, 3, 4, 5]; // 频道 0 首页 1 闪购 2 秒发3 社区 4 兔头 5个人中心

        //先判断参数是否符合
        if (!empty($param['vinehoo_client_version']) &&
            isset($param['client']) && in_array($param['client'], $clients) &&
            isset($param['channel']) && in_array($param['channel'], $channels)) {
            //根据客户端判断版本

            // 当前 安卓版本号 9.0.3 IOS版本号 9.08 --2022年10月9日15:04:42
            if (($param['client'] == 0 && $param['vinehoo_client_version'] < 9.08) ||
                ($param['client'] == 1 && $param['vinehoo_client_version'] < 9.04)) {

                #region 取缓存
                $redis     = Cache::handler();
                $rediskeys = 'vinehoo.label.snapshoot.data';
                $cache_key = "client_{$param['client']}_channel_{$param['channel']}";

                $data = $redis->hget($rediskeys, $cache_key);
                #endregion

                #region 如未设置缓存, 更新缓存
                if ($data === false) {
                    $label_data = json_decode(file_get_contents(app()->getAppPath() . 'labelSnapshoot.json'), true);
                    foreach ($label_data as $key => $item) {
                        $redis->hset($rediskeys, $key, json_encode($item));
                    }
                    $data = $redis->hget($rediskeys, $cache_key);
                }
                #endregion

                #region 找到缓存直接返回,未设置缓存, 按接口获取
                if ($data !== false) {
                    return json_decode($data, true);
                }
                #endregion

            }

            // 当前 安卓版本号 9.0.7 IOS版本号 9.13 --2023年1月17日10:09:30
            elseif (($param['client'] == 0 && $param['vinehoo_client_version'] < 9.14) ||
                ($param['client'] == 1 && $param['vinehoo_client_version'] < 9.08)) {

                #region 取缓存
                $redis     = Cache::handler();
                $rediskeys = 'vinehoo.label.snapshoot.data2';
                $cache_key = "client_{$param['client']}_channel_{$param['channel']}";

                $data = $redis->hget($rediskeys, $cache_key);
                #endregion

                #region 如未设置缓存, 更新缓存
                if ($data === false) {
                    $label_data = json_decode(file_get_contents(app()->getAppPath() . 'labelSnapshoot2.json'), true);
                    foreach ($label_data as $key => $item) {
                        $redis->hset($rediskeys, $key, json_encode($item));
                    }
                    $data = $redis->hget($rediskeys, $cache_key);
                }
                #endregion

                #region 找到缓存直接返回,未设置缓存, 按接口获取
                if ($data !== false) {
                    return json_decode($data, true);
                }
                #endregion

            }

            // 当前 安卓版本号 9.1.0 IOS版本号 9.16 --2023年2月24日15:37:44
            elseif (($param['client'] == 0 && $param['vinehoo_client_version'] < 9.17) ||
                ($param['client'] == 1 && $param['vinehoo_client_version'] < 9.11)) {

                #region 取缓存
                $redis     = Cache::handler();
                $rediskeys = 'vinehoo.label.snapshoot.data2';
                $cache_key = "client_{$param['client']}_channel_{$param['channel']}";

                $data = $redis->hget($rediskeys, $cache_key);
                #endregion

                #region 如未设置缓存, 更新缓存
                if ($data === false) {
                    $label_data = json_decode(file_get_contents(app()->getAppPath() . 'labelSnapshoot3.json'), true);
                    foreach ($label_data as $key => $item) {
                        $redis->hset($rediskeys, $key, json_encode($item));
                    }
                    $data = $redis->hget($rediskeys, $cache_key);
                }
                #endregion

                #region 找到缓存直接返回,未设置缓存, 按接口获取
                if ($data !== false) {
                    return json_decode($data, true);
                }
                #endregion

            }

            // 当前 安卓版本号 9.1.2 IOS版本号 9.18 --2023年4月7日16:33:36
            elseif (($param['client'] == 0 && $param['vinehoo_client_version'] < 9.19) ||
                ($param['client'] == 1 && $param['vinehoo_client_version'] < 9.13)) {

                #region 取缓存
                $redis     = Cache::handler();
                $rediskeys = 'vinehoo.label.snapshoot.data4';
                $cache_key = "client_{$param['client']}_channel_{$param['channel']}";

                $data = $redis->hget($rediskeys, $cache_key);
                #endregion

                #region 如未设置缓存, 更新缓存
                if ($data === false) {
                    $label_data = json_decode(file_get_contents(app()->getAppPath() . 'labelSnapshoot4.json'), true);
                    foreach ($label_data as $key => $item) {
                        $redis->hset($rediskeys, $key, json_encode($item));
                    }
                    $data = $redis->hget($rediskeys, $cache_key);
                }
                #endregion

                #region 找到缓存直接返回,未设置缓存, 按接口获取
                if ($data !== false) {
                    return json_decode($data, true);
                }
                #endregion

            }
        }
        #endregion

        $where = function($query) use($param) {
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', $param['channel']);
            }
            if (isset($param['client']) && strlen($param['client']) > 0) {
                $query->where('client','like', "%".$param['client']."%");
            }
            $query->where('status',1);

            if (
                ($param['vinehoo_client'] == 'android' && $param['vinehoo_client_version'] < 9.43) ||
                ($param['vinehoo_client'] == 'ios' && $param['vinehoo_client_version'] < 9.49) ||
                ($param['vinehoo_client'] == 'harmonyos' && $param['vinehoo_client_version'] < 9.42)
            ) {
                $query->where('label_name','!=', '礼品卡');
            }

        };

        // 列表
        $list = LabelModel::getInf([
            'where' => $where,
            'with' => [
                'client_path' => function($query) {
                    $query->find();
                },
                'ad_path_param' => function($query) {
                    $query->find();
                }
            ],
            'field'=>'id,channel,label_name,icon,param,badge,path_id,page_area',
            'order' => ['page_area' => 'asc', 'sort' => 'desc', 'created_at' => 'desc'],
            'page' => $param['page'] ?? 1,
            'limit' => $param['limit'] ?? 50,
            'type' => 'select'
        ]);

        $label_channel = config('config.ad_capsule_channel');
        foreach($list as $key => $val) {
            $list[$key]['created_at'] = date('Y-m-d H:i:s', $val['created_at']);
            $list[$key]['icon'] = $val['icon'] ? env('ALIURL') . $val['icon'] : "";

            if(isset($val['icon'])){
                $sjf = strpos($val['icon'],'?')?"&":"?";
                $extstr = strstr($val['icon'],"?",true)?:$val['icon'];
                $w =84; $h=84;$ext = strrev(strchr(strrev($extstr),'.',true));//后缀;//后缀
                $list[$key]['icon'] = $val['icon'] ? $val['icon'].$sjf.'x-oss-process=image/resize,w_'.$w.',h_'.$h.'/auto-orient,1/quality,q_90/format,'.$ext : "";
            }
            $list[$key]['badge'] = $val['badge'] ? env('ALIURL') . $val['badge'] : "";

            foreach ($label_channel as $k => $v) {
                if ($val['channel'] == $v['id']) {
                    $list[$key]['channel_name'] = $v['name'];
                }
            }


        }

        // 总条数
        $total = LabelModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
            'list' => $list,
            'total' => $total
        ];
    }

    static function detail($param)
    {
        $obj= new LabelModel();
        $resoult = $obj->find($param['id'])->append(['la_path_value_param']);
        $resoult['icon'] = $resoult['icon'] ? env('ALIURL') . $resoult['icon'] : "";
        $resoult['badge'] = $resoult['badge'] ? env('ALIURL') . $resoult['badge'] : "";
        return $resoult;
    }

}
