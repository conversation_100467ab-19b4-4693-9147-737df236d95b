<?php

namespace app\service\v3;

use app\model\Tag as TagModel;
use app\model\LabelPathExtends;
use think\facade\Cache;
use think\Request;

class Tag
{
    static function getChannel() {
        return [
            0 => "闪购", 
            1 => "跨境", 
            2 => "烈酒" ,
            3 => "尾货" ,
        ];
    }

    /**
     * 列表
     * @return array
     */
    static function list($param) {
        $where = function($query) use($param) {
            if (isset($param['channel']) && strlen($param['channel']) > 0) {
                $query->where('channel', $param['channel']);
            }

            if (isset($param['status']) && strlen($param['status']) > 0) {
                $query->where('status', $param['status']);
            }
        };

        // 列表
        $list = TagModel::getInf([
            'where' => $where,
            'order' => ['sort' => 'desc', 'created_at' => 'desc'],
            'page' => $param['page'] ?? 1,
        	'limit' => $param['limit'] ?? 10,
            'type' => 'select'
    	]);

        foreach($list as $key => $val) {
            $list[$key]['created_at'] = date('Y-m-d H:i:s', $val['created_at']);
            $list[$key]['update_at'] = date('Y-m-d H:i:s', $val['update_at']);
            $list[$key]['channel_name'] = self::getChannel()[$val['channel']] ?? '';
        }

        // 总条数
    	$total = TagModel::getInf([
            'where' => $where,
            'type' => 'count'
        ]);

        return [
        	'list' => $list,
        	'total' => $total
        ];
    }

    /**
     * 添加
     * @return bool
     */
    static function create($param) {

        $TagModel = new TagModel();
        $id = $TagModel->strict(false)->insertGetId($param);
        if (!$id) {
            excep('添加异常');
        }
        return true;
    }

    /**
     * 编辑
     * @return bool
     */
    static function update($param) {
        $info = TagModel::getInf([
            'where' => function($query) use($param) {
                $query->where('id', $param['id']);
            },
            'field' => 'id',
            'type' => 'find'
        ]);
        if (!$info) {
            excep('标签不存在');
        }

        if (!$info->save($param)) {
            excep('编辑异常');
        }
        return true;
    }

}
