<?php


namespace app\service\v3;


use think\facade\Db;

class TopThreeOrder
{
    public function list($params)
    {
        $offset       = ($params['page'] - 1) * $params['limit'];
        $where        = [];
        $totalNum     = Db::name('top_three_order_discount')->where($where)->count();
        $lists        = Db::name('top_three_order_discount')->where($where)->limit($offset, $params['limit'])->order('id desc')->select()->toArray();
        $admin_id_arr = array_unique(array_column($lists, 'operator'));
        $admin_id_str = implode(',', $admin_id_arr);
        $adminInfo    = curlRequest(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => $admin_id_str, 'field' => 'realname'], [], 'GET');
        foreach ($lists as $key => $val) {
            $lists[$key]['coupon_name'] = Db::name('coupon')->where(['id' => $val['data_id']])->value('coupon_name');
            $lists[$key]['update_time'] = !empty($val['update_time']) ? date('Y-m-d H:i:s', $val['update_time']) : '';
            $lists[$key]['operator']    = isset($adminInfo['data'][$val['operator']]) ? $adminInfo['data'][$val['operator']] : '系统';
        }
        $result['list']  = $lists;
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:首三单修改
     * Author: zrc
     * Date: 2023/6/25
     * Time: 15:36
     * @param $params
     * @return int
     * @throws \app\CustomException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function update($params)
    {
        $discountInfo = Db::name('top_three_order_discount')->where(['id' => $params['id']])->find();
        if (empty($discountInfo)) excep('未获取到数据信息');
        $updateData                = [];
        $updateData['operator']    = $params['operator'];
        $updateData['update_time'] = time();
        if (!empty($params['type']) && in_array($params['type'], [1])) {
            $updateData['type'] = $params['type'];
        }
        if (!empty($params['data_id'])) {
            //优惠券监测是否失效
            if ((!empty($params['type']) && $params['type'] == 1) || (empty($params['type']) && $discountInfo['type'] == 1)) {
                $couponInfo = Db::name('coupon')->field('id,status,invalidate_time')->where(['id' => $params['data_id']])->find();
                if (empty($couponInfo)) excep('未获取到优惠券');
                if (($couponInfo['invalidate_time'] > 0 && $couponInfo['invalidate_time'] <= time()) || $couponInfo['status'] == 2) excep($params['data_id'] . '-优惠券已失效或已禁用');
            }
            $updateData['data_id'] = $params['data_id'];
        }
        if (!empty($params['status']) && in_array($params['status'], [1, 2])) {
            //福利开启生效对数据进行验证
            if ($params['status'] == 1) {
                if (empty($discountInfo['data_id'])) excep('没有配置优惠券ID');
                $couponInfo = Db::name('coupon')->field('id,status,invalidate_time')->where(['id' => $discountInfo['data_id']])->find();
                if (($couponInfo['invalidate_time'] > 0 && $couponInfo['invalidate_time'] <= time()) || $couponInfo['status'] == 2) excep($discountInfo['data_id'] . '-优惠券已失效或已禁用');
            }
            $updateData['status'] = $params['status'];
        }
        $result = Db::name('top_three_order_discount')->where(['id' => $params['id']])->update($updateData);
        return $result;
    }
}