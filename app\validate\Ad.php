<?php

namespace app\validate;

use think\Validate;

class Ad extends Validate
{
    /**
     * @var string[] `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(30) COLLATE utf8_unicode_ci NOT NULL,
    `client` tinyint(5) DEFAULT NULL COMMENT '客户端   1 iOS 2 安卓 3 小程序 4 h5 5 pc',
    `channel` tinyint(5) DEFAULT NULL COMMENT '频道 1 首页 2 闪购 3 秒发 4 社区 5 兔头',
    `image` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图片',
    `path` int(10) DEFAULT NULL COMMENT '路径',
    `sort` int(11) DEFAULT '0' COMMENT '排序',
    `status` tinyint(2) DEFAULT '1' COMMENT '状态  0禁用 1启用',
    `type` tinyint(2) DEFAULT NULL COMMENT '类型 1banner 2 开屏 3 弹窗 4 胶囊',
    `pattern
     */
    protected $rule = [
      'id'=>'require',
      'title|标题'=>'require|max:100',
//      'client'=>'require',
//      'channel'=>'require',
//      'image'=>'require',
      'path|路径'=>'require',
      'sort'=>'require',
      'type'=>'require',
      'start_time|上架时间'=>'require|date',
      'end_time|下架时间'=>'require|date',
//      'pattern'=>'require',
      'operator_id'=>'require',
        'status'=>'number'
    ];
    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证 111
    protected $scene = [
        'ad/status'  =>  ['status','id'],
    ];

    public function scenecreate()
    {
        return $this->remove('id', 'require');
    }

    public function scenestatus()
    {
        return $this->only(['id','status'])->append("status","require");
    }
}