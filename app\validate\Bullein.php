<?php

namespace app\validate;

use think\Validate;

class Bullein extends Validate
{
    protected $rule = [
        'operator_id'  =>  'require',
        'title|标题'  =>  'require',
        'context|内容'  =>  'require',
        'status'  =>  'require',
        'start_time|上架时间'  =>  'require|date',
        'end_time|下架时间'  =>  'require|date|>:start_time',
    ];

    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证
    protected $scene = [
        'bullein/form'  =>  ['operator_id', 'title', 'context','start_time','end_time'],
        'bullein/login'  =>  ['operator_id'],
        'bullein/is_show'  =>  ['status'],
    ];
}