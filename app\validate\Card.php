<?php

namespace app\validate;

use think\Validate;

class Card extends Validate
{
    protected $rule = [
        'operator_id'  =>  'require',
        'channel'  =>  'require',
        'card_name'  =>  'require',
        'pattern'  =>  'require',
        'style'  =>  'require',
        'sort'  =>  'require',
        'id'  =>  'require',
        'name'  =>  'require',
        'card_id'  =>  'require',
        'add_method'  =>  'require',
    ];

    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证
    protected $scene = [
        'card/create'  =>  ['operator_id', 'channel', 'card_name', 'pattern', 'style', 'sort'],
        'card/update'  =>  ['operator_id', 'card_name', 'sort'],
        'card/status'  =>  ['operator_id', 'status'],
        'card/delect'  =>  ['id'],
        'cardfilterlist'  =>  ['cid'],
        'cardfilteradd'  =>  ['name','card_id','sort','add_method'],
        'cardfilteredit'  =>  ['id','name','card_id','sort','add_method'],
        'cardfilterdel'  =>  ['id'],
        'clientgoodslist'  =>  [],  // 支持id或cid参数，在Service中验证
        'card/detail'  =>  ['id'],
    ];
}