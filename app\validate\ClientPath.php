<?php

namespace app\validate;

use think\Validate;

class ClientPath extends Validate
{
    protected $rule = [
        'operator_id'  =>  'require',
        'path_name'  =>  'require',
        'code'  =>  'require|unique:client_path|alphaNum',
        'client'  =>  'require',
        'android_path'  =>  'require',
        'mini_path'  =>  'require',
        'h5_path'  =>  'require',
    ];

    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证
    protected $scene = [
        'clientpath/form'  =>  ['operator_id', 'path_name', 'client','code'],//, 'android_path', 'mini_path', 'h5_path'
    ];
}