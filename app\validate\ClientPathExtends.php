<?php

namespace app\validate;

use think\Validate;

class ClientPathExtends extends Validate
{
    protected $rule = [
        'id'=>'require',
        'pid'=>'require',
        'title'=>'require',
        'ios_key'=>'require',
        'ios_val'=>'require',
        'android_key'=>'require',
        'android_val'=>'require',
        'mini_key'=>'require',
        'mini_val'=>'require',
        'h5_key'=>'require',
        'h5_val'=>'require',
        ];

    protected function checkValue($value, $rule, $data=[]){

    }

}