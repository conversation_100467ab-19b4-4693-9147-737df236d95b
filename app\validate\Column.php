<?php

namespace app\validate;

use think\Validate;

class Column extends Validate
{
    protected $rule = [
        'operator_id|操作人'  =>  'require',
        'channel|频道'  =>  'require',
        'client|客户端' => 'require',
        'name|名称'  =>  'require',
        'icon|图标' => 'require',
        'badge|徽章' => 'max:255',
        'goods_sort_type|商品排序方式'  =>  'require|number|>=:0',
        'add_method|添加方式'  =>  'number|>=:0',
        'sort|排序值'  =>  'require|number|>=:0',
        'id|ID'  =>  'require|number|>=:0',
        'cid|栏目ID'  =>  'require|number|>=:0',
        'card_id|栏目ID'  =>  'require|number|>=:0',
    ];

    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证
    protected $scene = [
        'column/create'  =>  ['operator_id', 'channel', 'client', 'name', 'goods_sort_type', 'add_method', 'sort', 'icon', 'badge'],
        'column/update'  =>  ['id','operator_id', 'channel', 'client', 'name', 'goods_sort_type', 'add_method', 'sort', 'icon', 'badge'],
        'column/status'  =>  ['id', 'operator_id', 'status'],
        'column/delect'  =>  ['id'],
        'column/content'  =>  ['id','name'],
        'filtergoodslist'  =>  ['cid'],
        'columnfilterlist'  =>  ['cid'],
        'columnfilteradd'  =>  ['name','card_id','sort','add_method'],
        'columnfilteredit'  =>  ['id','name','card_id','sort','add_method'],
        'columnfilterdel'  =>  ['id'],

    ];
}