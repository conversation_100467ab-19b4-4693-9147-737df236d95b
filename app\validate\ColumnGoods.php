<?php

namespace app\validate;

use think\Validate;

class ColumnGoods extends Validate
{
    protected $rule = [
        'operator_id|操作人ID'  =>  'require',
        'period|期数'  =>  'require',
        'periods_type|频道'  =>  'require',
        'sort|排序值'  =>  'require',
        'status|状态' => 'require',
        'cid|栏目ID' => 'require',
        'id|记录ID' => 'require',
        'filter_id|筛选项ID' => 'require',
    ];

    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证
    protected $scene = [
        'list'  =>  ['cid'],
        'create'  =>  ['cid', 'period', 'periods_type', 'status', 'sort'],
        'update'  =>  ['id', 'period', 'periods_type', 'status', 'sort'],
        'login'  =>  ['id','operator_id'],
        'getfiltergoodslist'  =>  ['cid','filter_id'],
    ];
}