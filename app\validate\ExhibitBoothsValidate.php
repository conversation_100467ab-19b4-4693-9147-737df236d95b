<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class ExhibitBoothsValidate
 * @package app\validate
 */
class ExhibitBoothsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数' => 'number|gt:0',
        'page|当前页'    => 'number|gt:0',

        'id|主键ID'            => 'number',  //主键ID
        'name|展台名称'        => 'require|max:255',  //展台名称
        'number|展台编号'      => 'require|max:50',  //展台编号
        'exhibition_id|展会ID' => 'require|number',  //展会ID
        'updated_at|更新时间'  => 'date',  //更新时间
        'operator_id|操作人ID' => 'number',  //操作人ID

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'name', 'number', 'exhibition_id', 'updated_at', 'operator_id',],
        'edit'   => ['id', 'name', 'number', 'exhibition_id', 'updated_at', 'operator_id',],
        'detail' => ['id',],
        'del'    => ['id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'name', 'number', 'exhibition_id', 'updated_at', 'operator_id',])->remove('id', 'require')
            ->remove('name', 'require')
            ->remove('number', 'require')
            ->remove('exhibition_id', 'require');
    }


}