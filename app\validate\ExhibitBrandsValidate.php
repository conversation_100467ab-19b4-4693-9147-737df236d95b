<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class ExhibitBrandsValidate
 * @package app\validate
 */
class ExhibitBrandsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键ID' => 'require|number',  //主键ID
        'name|品牌名称' => 'require|max:255',  //品牌名称
        'logo_url|品牌Logo URL' => 'max:255',  //品牌Logo URL
        'description|品牌介绍' => 'max:200',  //品牌介绍
//        'is_own_brand|是否为自有品牌' => 'number',  //是否为自有品牌
        'exhibition_id|展会ID,外键,关联到展会表' => 'number',  //展会ID，外键，关联到展会表
        'updated_at|更新时间' => 'date',  //更新时间
        'operator_id|操作人ID' => 'number',  //操作人ID
        'booth_id|展台ID,外键,关联到展台表' => 'number',  //展台ID，外键，关联到展台表
        'brand_about_section_image_url|关于品牌栏目图片URL' => 'max:255',  //关于品牌栏目图片URL
        'brand_description_text_color|描述文字颜色值' => 'max:7',  //描述文字颜色值
        'brand_middle_color_value|品牌中部颜色值' => 'max:7',  //品牌中部颜色值
        'brand_bottom_image_url|品牌底部图片URL' => 'max:255',  //品牌底部图片URL
        'top_image_url|顶部图片URL' => 'max:255',  //顶部图片URL

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'name', 'logo_url', 'description', 'is_own_brand', 'exhibition_id', 'updated_at', 'operator_id', 'booth_id', 'brand_about_section_image_url', 'brand_description_text_color', 'brand_middle_color_value', 'brand_bottom_image_url', 'top_image_url',],
        'edit'   => [ 'id', 'name', 'logo_url', 'description', 'is_own_brand', 'exhibition_id', 'updated_at', 'operator_id', 'booth_id', 'brand_about_section_image_url', 'brand_description_text_color', 'brand_middle_color_value', 'brand_bottom_image_url', 'top_image_url',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'name', 'logo_url', 'description', 'is_own_brand', 'exhibition_id', 'updated_at', 'operator_id', 'booth_id', 'brand_about_section_image_url', 'brand_description_text_color', 'brand_middle_color_value', 'brand_bottom_image_url', 'top_image_url', ])->remove('id', 'require')
            ->remove('name', 'require')
            ;
    }




}