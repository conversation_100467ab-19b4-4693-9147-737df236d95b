<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class ExhibitExhibitionsValidate
 * @package app\validate
 */
class ExhibitExhibitionsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键ID' => 'require|number',  //主键ID
        'name|展会名称' => 'require|max:255',  //展会名称
        'description|展会描述' => 'max:65535',  //展会描述
        'start_date|展会开始日期' => 'require|date',  //展会开始日期
        'end_date|展会结束日期' => 'require|date',  //展会结束日期
        'top_image_url|顶部图片URL' => 'max:255',  //顶部图片URL
        'featured_products_button_image_url|精选产品按钮图片URL' => 'max:255',  //精选产品按钮图片URL
        'featured_products_button_h_image_url|精选产品按钮图片URL(选中)' => 'max:255',  //精选产品按钮图片URL(选中)
        'exhibiting_brands_button_image_url|参展品牌按钮图片URL' => 'max:255',  //参展品牌按钮图片URL
        'exhibiting_brands_button_h_image_url|参展品牌按钮图片URL(选中)' => 'max:255',  //参展品牌按钮图片URL(选中)
        'featured_products_section_image_url|精选产品栏目图片URL' => 'max:255',  //精选产品栏目图片URL
        'exhibiting_brands_section_image_url|参展品牌栏目图片URL' => 'max:255',  //参展品牌栏目图片URL
        'bottom_image_url|底部图片URL' => 'max:255',  //底部图片URL
        'middle_color_value|中部颜色值' => 'max:7',  //中部颜色值
        'brand_about_section_image_url|关于品牌栏目图片URL' => 'max:255',  //关于品牌栏目图片URL
        'brand_description_text_color|描述文字颜色值' => 'max:7',  //描述文字颜色值
        'brand_middle_color_value|品牌中部颜色值' => 'max:7',  //品牌中部颜色值
        'brand_bottom_image_url|品牌底部图片URL' => 'max:255',  //品牌底部图片URL
        'updated_at|更新时间' => 'date',  //更新时间
        'operator_id|操作人ID' => 'number',  //操作人ID

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'name', 'description', 'start_date', 'end_date', 'top_image_url', 'featured_products_button_image_url', 'featured_products_button_h_image_url', 'exhibiting_brands_button_image_url', 'exhibiting_brands_button_h_image_url', 'featured_products_section_image_url', 'exhibiting_brands_section_image_url', 'bottom_image_url', 'middle_color_value', 'brand_about_section_image_url', 'brand_description_text_color', 'brand_middle_color_value', 'brand_bottom_image_url', 'updated_at', 'operator_id',],
        'edit'   => [ 'id', 'name', 'description', 'start_date', 'end_date', 'top_image_url', 'featured_products_button_image_url', 'featured_products_button_h_image_url', 'exhibiting_brands_button_image_url', 'exhibiting_brands_button_h_image_url', 'featured_products_section_image_url', 'exhibiting_brands_section_image_url', 'bottom_image_url', 'middle_color_value', 'brand_about_section_image_url', 'brand_description_text_color', 'brand_middle_color_value', 'brand_bottom_image_url', 'updated_at', 'operator_id',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'name', 'description', 'start_date', 'end_date', 'top_image_url', 'featured_products_button_image_url', 'featured_products_button_h_image_url', 'exhibiting_brands_button_image_url', 'exhibiting_brands_button_h_image_url', 'featured_products_section_image_url', 'exhibiting_brands_section_image_url', 'bottom_image_url', 'middle_color_value', 'brand_about_section_image_url', 'brand_description_text_color', 'brand_middle_color_value', 'brand_bottom_image_url', 'updated_at', 'operator_id', ])->remove('id', 'require')
            ->remove('name', 'require')
            ->remove('start_date', 'require')
            ->remove('end_date', 'require')
            ;
    }




}