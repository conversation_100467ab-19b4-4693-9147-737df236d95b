<?php

namespace app\validate;

use think\Validate;

/**
 * app\model
 * Class ExhibitProductsValidate
 * @package app\validate
 */
class ExhibitProductsValidate extends Validate
{

    protected $rule = [
        'limit|返回条数'          => 'number|gt:0',
        'page|当前页'             => 'number|gt:0',

        'id|主键ID' => 'require|number',  //主键ID
//        'name|商品名称' => 'require|max:255',  //商品名称
        'product_title|商品标题' => 'require|max:255',  //商品标题
        'description|商品描述' => 'max:65535',  //商品描述
        'price|商品价格' => 'require|float|>=:0',  //商品价格
        'market_price|市场价' => 'require|float|>=:0',  //市场价
        'product_image_url|产品图片URL' => 'max:255',  //产品图片URL
//        'is_featured|是否精选' => 'number',  //是否精选
        'brand_id|品牌ID,外键,关联到品牌表' => 'require|number',  //品牌ID，外键，关联到品牌表
        'exhibition_id|展会ID,外键,关联到展会表' => 'require|number',  //展会ID，外键，关联到展会表
        'operator_id|操作人ID' => 'number',  //操作人ID
        'period|商品ID' => 'require|number',  //商品ID
        'booth_id|展台ID,外键,关联到展台表' => 'require|number',  //展台ID，外键，关联到展台表
        'sort|排序' => 'require',  //展台ID，外键，关联到展台表

    ];


    protected $scene = [
        'add'    => ['vh_uid', 'vh_vos_name', 'name', 'product_title','sort', 'description', 'price', 'market_price', 'product_image_url', 'is_featured', 'brand_id', 'exhibition_id', 'operator_id', 'period', 'booth_id',],
        'edit'   => [ 'id', 'name', 'product_title', 'description', 'sort', 'price', 'market_price', 'product_image_url', 'is_featured', 'brand_id', 'exhibition_id', 'operator_id', 'period', 'booth_id',],
        'detail' => [ 'id',],
        'del'    => [ 'id',],
    ];

    public function scenelist()
    {
        return $this->only(['limit', 'page', 'fields', 'id', 'name', 'product_title', 'description', 'price', 'market_price', 'product_image_url', 'is_featured', 'brand_id', 'exhibition_id', 'operator_id', 'period', 'booth_id', ])->remove('id', 'require')
            ->remove('name', 'require')
            ->remove('product_title', 'require')
            ->remove('price', 'require')
            ->remove('market_price', 'require')
            ->remove('period', 'require')
            ->remove('brand_id', 'require')
            ->remove('exhibition_id', 'require')
            ->remove('booth_id', 'require')
            ;
    }




}