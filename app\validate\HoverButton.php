<?php

namespace app\validate;

use think\Validate;

class HoverButton extends Validate
{
    protected $rule = [
        'id'  =>  'require|number',
        'external_action|是否验证新用户'  =>  'require|number',
        'path|跳转类型'  =>  'require|max:3000',
        'params|参数'  =>  'require|array',
        'show|启用禁用'  =>  'require|number',
        'start_time|上架时间'  =>  'require|date',
        'end_time|下架时间'  =>  'require|date',
    ];

    protected $scene = [
        'edit'  =>  ['id','params','show','path','external_action','start_time','end_time'],
        'showsave'  =>  ['id','show'],
        'create'  =>  ['params','show','path','external_action','start_time','end_time'],
    ];

}