<?php

namespace app\validate;

use think\Validate;

class Tag extends Validate
{
    protected $rule = [
        'id|标签ID'  =>  'require|number|>=:0',
        'operator_id'  =>  'require',
        'channel|频道'  =>  'require|number|>=:0',
        'label_name'  =>  'require',
        'label_id|商品标签ID'  =>  'require|number|>:0',
        'sort|排序值'  =>  'require|number|>=:0',
        'status|状态'  =>  'require|number|in:0,1',
    ];

    protected $message = [
        'operator_id' => '请先登录',
    ];

    // 场景验证
    protected $scene = [
        'tag/form'  =>  ['operator_id', 'channel', 'label_name', 'label_id', 'status', 'sort'],
        'tag/update'  =>  ['id', 'operator_id', 'channel', 'label_name', 'label_id', 'status', 'sort'],
        'tag/status'  =>  ['operator_id', 'status'],
    ];
}