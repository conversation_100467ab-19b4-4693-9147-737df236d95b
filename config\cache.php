<?php

// +----------------------------------------------------------------------
// | 缓存设置
// +----------------------------------------------------------------------

return [
    // 默认缓存驱动
    'default' => 'redis',

    // 缓存连接方式配置
    'stores'  => [
        'file' => [
            // 驱动方式
            'type'       => 'File',
            // 缓存保存目录
            'path'       => '',
            // 缓存前缀
            'prefix'     => '',
            // 缓存有效期 0表示永久缓存
            'expire'     => 0,
            // 缓存标签前缀
            'tag_prefix' => 'tag:',
            // 序列化机制 例如 ['serialize', 'unserialize']
            'serialize'  => [],
        ],
        // 更多的缓存连接
        'redis' => [
            // 驱动方式
            'type'       => env("CACHE.DRIVER","redis"),
            // 缓存前缀
            'prefix'     => env("CACHE.prefix","vinehoo."),
            //地址
            'host'          => env("CACHE.HOST","127.0.0.1"),
            //  端口号
            'port'      => env("CACHE.PORT",6379),
            //  密码
            'password'=> env("CACHE.PASSWORD","vinehoo."),
            //  默认缓存时间
            'expire' => 0
        ],
    ],
];
