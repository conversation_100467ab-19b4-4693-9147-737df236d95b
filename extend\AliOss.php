<?php

use OSS\Core\OssException;
use OSS\OssClient;

class AliOss
{

    protected $accessKeyId     = '';
    protected $accessKeySecret = '';
    protected $endpoint        = '';
    protected $bucket          = '';
    protected $oss_client      = null;

    public function __construct()
    {
        $this->accessKeyId     = env('OSS.ACCESSKEYID');
        $this->accessKeySecret = env('OSS.ACCESSKEYSECRET');
        $this->endpoint        = env('OSS.ENDPOINT');
        $this->bucket          = env('OSS.BUCKET');
        $this->oss_client      = new OssClient($this->accessKeyId, $this->accessKeySecret, $this->endpoint);
    }


    /**
     * @方法描述: 上传文件到OSS
     * <AUTHOR>
     * @Date 2022/9/7 11:06
     * @param $file_path
     * @param $upload_path
     * @return bool|string
     */
    public function uploadFile($file_path, $upload_path)
    {
        try {
            $result = $this->oss_client->putObject($this->bucket, $upload_path, file_get_contents($file_path));
        } catch (OssException $e) {
            return "上传OSS失败: " . $e->getMessage();
        }
        return true;
    }


    /**
     * @方法描述: 下载OSS文件到本地
     * <AUTHOR>
     * @Date 2022/9/7 11:09
     * @param $oss_path
     * @param $local_path
     * @return bool|string
     */
    public function download($oss_path, $local_path)
    {

        $options = [OssClient::OSS_FILE_DOWNLOAD => $local_path];
        try {
            $this->oss_client->getObject($this->bucket, $oss_path, $options);
        } catch (OssException $e) {
            //下载失败
            return "$oss_path 下载失败 " . $e->getMessage();
        }
        return true;
    }
}