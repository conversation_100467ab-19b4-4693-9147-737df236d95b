<?php

use AlibabaCloud\SDK\Cdn\V20180510\Cdn as AliCdn;
use AlibabaCloud\SDK\Cdn\V20180510\Models\RefreshObjectCachesRequest;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Darabonba\OpenApi\Models\Config;

class CDN
{

    protected $accessKeyId     = '';
    protected $accessKeySecret = '';

    public function __construct()
    {
        $this->accessKeyId     = "LTAI5tA399hBWprfBqq9m2hj";
        $this->accessKeySecret = "******************************";
        $this->endpoint        = "cdn.aliyuncs.com";
    }

    public function refresh($exhib_id = 0, $booth_id = 0)
    {
        try {
            $config                  = new Config();
            $config->accessKeyId     = $this->accessKeyId;
            $config->accessKeySecret = $this->accessKeySecret;
            $config->endpoint        = $this->endpoint;
            $cdn                     = new AliCdn($config);

            $objectPath = [];
            if ($exhib_id) $objectPath[] = env('OSS.ALIURL') . "/vinehoo/exhib/exhib_{$exhib_id}.json";
            if ($booth_id) $objectPath[] = env('OSS.ALIURL') . "/vinehoo/exhib/exhib_booth_{$booth_id}.json";
            $request_param = [
                'objectPath' => implode('\n', $objectPath)
            ];

            $refreshObjectCachesRequest = new RefreshObjectCachesRequest($request_param);
            $runtime                    = new RuntimeOptions([]);
            $refresh_object             = $cdn->refreshObjectCachesWithOptions($refreshObjectCachesRequest, $runtime);

            $PushObjectCacheRequest = new \AlibabaCloud\SDK\Cdn\V20180510\Models\PushObjectCacheRequest($request_param);
            $push_cache             = $cdn->pushObjectCacheWithOptions($PushObjectCacheRequest, $runtime);
        } catch (\Exception $e) {
            \think\facade\Log::write('刷新预热CDN错误: ' . $e->getMessage() . ' LINE:' . $e->getLine());
        }
        return true;
    }

}

