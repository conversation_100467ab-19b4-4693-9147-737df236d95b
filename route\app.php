<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;
use think\facade\Request;

Route::rule('/', function () {
    return 'tp6-marketing-conf';
});

//获取header中的接口版本号
$version = Request::header('api-version') ?? 'v3';

// 公共
Route::group('marketing-conf/' . $version . '/common', function () {
    //获取频道
	Route::get('channel', 'channel');
    //修改统计数量  广告 金刚区
    Route::post('viewnums', 'viewnums');
    //修改统计数量  广告 金刚区
    Route::get('secondConfig', 'secondConfig');
})->prefix('marketing-conf/' . $version . '.Common/');

// 默认关键词
Route::group('marketing-conf/' . $version . '/defaultkeyword', function () {
	Route::get('list', 'list');
	Route::post('update', 'update');
	Route::get('getKeyword', 'getKeyword');
})->prefix('marketing-conf/' . $version . '.DefaultKeyword/');

// 快报
Route::group('marketing-conf/' . $version . '/bullein', function () {
	Route::get('list', 'list');
	Route::post('create', 'create');
	Route::post('update', 'update');
	Route::post('delete', 'delete');
	Route::post('status', 'status');
	Route::post('checked', 'checked');
	Route::get('getChecked', 'getChecked');
	Route::get('detail', 'detail');
    Route::get('clientlist', 'clientlist');
    Route::get('clientdetail', 'clientdetail');
})->prefix('marketing-conf/' . $version . '.Bullein/');

// 热门搜索关键词
Route::group('marketing-conf/' . $version . '/hotkeyword', function () {
	Route::get('list', 'list');
	Route::post('create', 'create');
	Route::post('update', 'update');
	Route::post('status', 'status');
	Route::post('delete', 'delete');
	Route::get('getChecked', 'getChecked');
    Route::get('clientlist', 'clientlist');
})->prefix('marketing-conf/' . $version . '.HotKeyword/');

// 客户端路径配置
Route::group('marketing-conf/' . $version . '/clientpath', function () {
	Route::get('list', 'list');
	Route::post('create', 'create');
	Route::post('update', 'update');
    Route::get('filterlist', 'filterlist');
    Route::get('codefilterlist', 'codefilterlist');
    Route::get('codefiltermorelist', 'codefiltermorelist');
    Route::get('multiplebyids', 'multiplebyids');
})->prefix('marketing-conf/' . $version . '.ClientPath/');

// 金刚区
Route::group('marketing-conf/' . $version . '/label', function () {
	Route::get('list', 'list');
    Route::get('detail', 'detail');
	Route::post('create', 'create');
	Route::post('update', 'update');
	Route::post('status', 'status');
    Route::get('clientlist', 'clientlist');
})->prefix('marketing-conf/' . $version . '.Label/');

// 卡片
Route::group('marketing-conf/' . $version . '/card', function () {
	Route::get('list', 'list');
	Route::get('detail', 'detail');
	Route::post('create', 'create');
	Route::post('update', 'update');
	Route::post('status', 'status');
	Route::post('delect', 'delect');
    Route::get('clientlist', 'clientlist');
    Route::get('mf_clientlist', 'mf_clientlist');
    Route::get('clientgoodslist', 'clientgoodslist');
    Route::post('goodslist', 'goodslist');
    Route::get('cardfilterlist', 'cardfilterlist');
    Route::post('cardfilteradd', 'cardfilteradd');
    Route::post('cardfilteredit', 'cardfilteredit');
    Route::post('cardfilterdel', 'cardfilterdel');
})->prefix('marketing-conf/' . $version . '.Card/');

Route::group('marketing-conf/' . $version . '/cardgoodslive', function () {
	Route::any('list', 'list');
	Route::post('create', 'create');
	Route::post('delete', 'delete');
	Route::post('update', 'update');
    Route::get('clientgoodslist', 'clientgoodslist');
    Route::get('filtergoodslist', 'filtergoodslist');
    Route::get('getfilter', 'getfilter');
})->prefix('marketing-conf/' . $version . '.CardGoodsLive/');
//广告
Route::group('marketing-conf/' . $version . '/ad', function () {
    Route::get('list', 'list');
    Route::get('detail', 'detail');
    Route::post('create', 'create');
    Route::post('update', 'update');
    Route::post('status', 'status');
    Route::get('clientlist', 'clientlist');//客户端过滤
    Route::get('clientmergelist', 'clientmergelist');//客户端广告合并
    Route::get('cachecreate', 'cacheCreate');//缓存创建
    Route::post('syncGoodsStatus', 'syncGoodsStatus');//同步商品状态
    Route::get('prescription', 'prescription');//时效广告 查询 胶囊
})->prefix('marketing-conf/' . $version . '.ad/');
// 悬浮按钮
Route::group('marketing-conf/' . $version . '/hoverButton', function () {
    Route::get('/', 'index');
    Route::get('list', 'list');
    Route::post('update', 'update');
    Route::post('create', 'create');
    Route::post('showsave', 'showsave');
    Route::post('delete', 'delete');
})->prefix('marketing-conf/' . $version . '.hoverButton/');

// 韦德活动拉新
Route::group('marketing-conf/' . $version . '/common', function () {
    Route::post('receivegiftforweide', 'sendGiftPackByUid');
})->prefix('marketing-conf/' . $version . '.common/');

/**
 * 抽奖
 */
Route::group('marketing-conf/' . $version . '/Game', function () {
    // 更新抽奖配置项
    Route::post('updateRotaryDraw', 'updateRotaryDraw');
    // 更新兔头抽奖配置项
    Route::post('updateRabbitRotaryDraw', 'updateRabbitRotaryDraw');
    // 更新新人兔头抽奖
    Route::post('updateNewRotaryDraw', 'updateNewRotaryDraw');
    // 更新黑名单抽奖配置项
    Route::post('updateRotaryDrawBlacklist', 'updateRotaryDrawBlacklist');
    // 获取抽奖配置项
    Route::get('getRotaryDraw', 'getRotaryDraw');
    // 获取黑名单抽奖配置项
    Route::get('getRotaryDrawBlacklist', 'getRotaryDrawBlacklist');
    // 抽奖
    Route::get('luckDraw', 'luckDraw');
    // 添加订单购买记录
    Route::post('salesOrderRecord', 'salesOrderRecord');
    // 兔头抽奖
    Route::get('rabbitLuckDraw', 'rabbitLuckDraw');
    // 兔头抽奖列表
    Route::get('rabbitShareDrawList', 'rabbitShareDrawList');
    // 用户兔头抽奖记录
    Route::get('getUserLotteryInfo', 'getUserLotteryInfo');
    // 获取兔头抽奖配置项
    Route::get('getRabbitRotaryDraw', 'getRabbitRotaryDraw');
    // 查询订单抽奖状态
    Route::get('getUserOrderStatus', 'getUserOrderStatus');
    // 剩余抽奖次数查询
    Route::get('getUserOrderCount', 'getUserOrderCount');
    // 新人兔头用户抽奖
    Route::get('newRabbitLuckDraw', 'newRabbitLuckDraw');
    // 获取新人抽奖配置项
    Route::get('getNewRabbitRotaryDraw', 'getNewRabbitRotaryDraw');
    // 新人抽奖中奖记录
    Route::get('newRabbitShareDrawList', 'newRabbitShareDrawList');
    // 新人抽奖记录
    Route::get('getNewcomerLotteryInfo', 'getNewcomerLotteryInfo');
    // 分享增加抽奖次数
    Route::post('newcomerShare', 'newcomerShare');



})->prefix('marketing-conf/' . $version . '.Game/');

/**
 * 首三单福利
 */
Route::group('marketing-conf/' . $version . '/TopThreeOrder', function () {
    Route::post('receivegiftforweide', 'sendGiftPackByUid');
    Route::get('list', 'list');//首三单列表
    Route::post('update', 'update');//首三单修改
})->prefix('marketing-conf/' . $version . '.TopThreeOrder/');

// 标签
Route::group('marketing-conf/' . $version . '/tag', function () {
	Route::get('list', 'list');
	Route::post('create', 'create');
	Route::post('update', 'update');
	Route::post('status', 'status');
    Route::get('clientlist', 'clientlist');
})->prefix('marketing-conf/' . $version . '.tag/');

// 栏目
Route::group('marketing-conf/' . $version . '/column', function () {
	Route::get('list', 'list');
    Route::get('detail', 'detail');
	Route::post('create', 'create');
	Route::post('update', 'update');
	Route::post('status', 'status');
	Route::post('delect', 'delect');
    Route::get('clientlist', 'clientlist');
    Route::get('clientgoodslist', 'clientgoodslist');
    Route::get('filtergoodslist', 'filtergoodslist');
    Route::post('goodslist', 'goodslist');
    Route::get('columnfilterlist', 'columnfilterlist');
    Route::post('columnfilteradd', 'columnfilteradd');
    Route::post('columnfilteredit', 'columnfilteredit');
    Route::post('columnfilterdel', 'columnfilterdel');
})->prefix('marketing-conf/' . $version . '.Column/');

Route::group('marketing-conf/' . $version . '/columngoods', function () {
	Route::any('list', 'list');
	Route::post('create', 'create');
	Route::post('delete', 'delete');
	Route::post('update', 'update');
    Route::get('getfilter', 'getfilter');
    Route::get('getfiltergoodslist', 'getfiltergoodslist');
})->prefix('marketing-conf/' . $version . '.ColumnGoods/');



Route::group("marketing-conf", function () {
    Route::group("v3", function () {

        Route::post("exhib/create/exhibition", "ExhibitExhibitions/add"); //添加
        Route::post("exhib/update/exhibition", "ExhibitExhibitions/edit"); //编辑
        Route::get("exhib/exhibitions", "ExhibitExhibitions/list"); //列表
        Route::get("exhib/detail/exhibitions", "ExhibitExhibitions/detail"); //详情
        Route::get("exhib/delete/exhibition", "ExhibitExhibitions/del"); //删除

        //展台表
        Route::post("exhib/create/booths", "ExhibitBooths/add"); //添加
        Route::post("exhib/update/booths", "ExhibitBooths/edit"); //编辑
        Route::get("exhib/booths", "ExhibitBooths/list"); //列表
        Route::get("exhib/detail/booths", "ExhibitBooths/detail"); //详情
        Route::get("exhib/delete/booths", "ExhibitBooths/del"); //删除

        //品牌表
        Route::post("exhib/create/brands", "ExhibitBrands/add"); //添加
        Route::get("exhib/update/brands", "ExhibitBrands/edit"); //编辑
        Route::get("exhib/brands", "ExhibitBrands/list"); //列表
        Route::get("exhib/detail/brands", "ExhibitBrands/detail"); //详情
        Route::get("exhib/delete/brands", "ExhibitBrands/del"); //删除

        //商品表
        Route::post("exhib/create/products", "ExhibitProducts/add"); //添加
        Route::post("exhib/update/products", "ExhibitProducts/edit"); //编辑
        Route::get("exhib/products", "ExhibitProducts/list"); //列表
        Route::get("exhib/detail/products", "ExhibitProducts/detail"); //详情
        Route::get("exhib/delete/products", "ExhibitProducts/del"); //删除
        Route::get("exhib/delete/products/quantitySold", "ExhibitProducts/quantitySold"); //获取已售数量
        Route::post("exhib/products/sync", "ExhibitProducts/sync"); //同步售卖状态

        ##########tp6-anchor##########
    })->prefix("v3.");
});
